import {
  StyleSheet,
  View,
  Dimensions,
  Text,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AdminBottomTabParamlist} from '../../../navigation/AdminBottomTabs';
import {colors, fonts} from '../../../utilities/theme';
import {AdminStackParamsList} from '../../../navigation/AdminNavigation';
import firestore from '@react-native-firebase/firestore';
import {IProducts} from '../../../interfaces/Products';
import AdminStoreItem from '../../../component/adminCommon/AdminStoreItem';
import DeleteConfimationModal from '../../../model/DeleteConfimationModal';
type Props = NativeStackScreenProps<
  AdminBottomTabParamlist & AdminStackParamsList,
  'Analytics'
>;
import {LineChart} from 'react-native-gifted-charts';

const Analytics: React.FC<Props> = ({navigation}) => {
  const [products, setProducts] = useState<IProducts[]>([]);
  const [productsLoading, setProductsLoading] = useState(false);

  const [selectedProduct, setSelectedProduct] = useState<IProducts | null>(
    null,
  );
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setProductsLoading(true);
    const unsubscribe = firestore()
      .collection('Products')
      .orderBy('updatedAt', 'desc')
      .onSnapshot(
        snapshot => {
          const _products = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
          })) as IProducts[];
          setProducts(_products);
          setProductsLoading(false);
        },
        error => {
          console.log('Error while fetching all products', error);
          setProductsLoading(false);
        },
      );
    return () => unsubscribe();
  }, []);

  const deleteProduct = () => {
    if (selectedProduct) {
      firestore()
        .collection('Products')
        .doc(selectedProduct.id)
        .delete()
        .then(() => {
          setProducts(prevProducts =>
            prevProducts.filter(item => item.id !== selectedProduct.id),
          );
          setIsVisible(false);
        })
        .catch(error => {
          console.error('Error deleting product: ', error);
        });
    }
  };

  const dataLine = [
    {value: 55},
    {value: 20},
    {value: 60},
    {value: 25},
    {value: 50},
    {value: 40},
  ];

  const ListFooterComponent = () => {
    return (
      <>
        <View style={styles.monthlyContainer}>
          <Text style={[styles.chartLabel, {marginBottom: 20}]}>
            Products Sales
          </Text>

          <LineChart
            areaChart
            curved
            data={dataLine}
            width={Dimensions.get('screen').width}
            height={200}
            initialSpacing={-6}
            maxValue={100}
            noOfSections={5}
            yAxisThickness={0}
            xAxisThickness={0}
            hideDataPoints={false}
            color={colors.secondary} // dots line color
            thickness={6} // line width thickness
            yAxisTextStyle={{color: '#999'}} // vertical text color
            startFillColor="#fcf8f5" // dots below shadow color
            rulesColor="#EEEEEE" // horizontal lines color
            xAxisLabelTextStyle={{
              color: '#999', // horizontal text color
              fontSize: 12,
              marginLeft: 25,
            }}
            xAxisLabelTexts={['10am', '11am', '12am', '01am', '02am']}
            rulesType="solid"
            xAxisLabelsVerticalShift={0}
            spacing={55}
            dataPointsColor={colors.primary} // dots color
            dataPointsRadius={6} // dot width
          />
        </View>
        <View style={styles.monthlyContainer}>
          <Text style={[styles.chartLabel, {marginBottom: 20}]}>
            Product Revenue
          </Text>

          <LineChart
            areaChart
            curved
            data={dataLine}
            width={Dimensions.get('screen').width}
            height={200}
            initialSpacing={-6}
            maxValue={100}
            noOfSections={5}
            yAxisThickness={0}
            xAxisThickness={0}
            hideDataPoints={false}
            color={colors.secondary} // dots line color
            thickness={6} // line width thickness
            yAxisTextStyle={{color: '#999'}} // vertical text color
            startFillColor="#fcf8f5" // dots below shadow color
            rulesColor="#EEEEEE" // horizontal lines color
            xAxisLabelTextStyle={{
              color: '#999', // horizontal text color
              fontSize: 12,
              marginLeft: 25,
            }}
            xAxisLabelTexts={['10am', '11am', '12am', '01am', '02am']}
            rulesType="solid"
            xAxisLabelsVerticalShift={0}
            spacing={55}
            dataPointsColor={colors.primary} // dots color
            dataPointsRadius={6} // dot width
          />
        </View>
      </>
    );
  };

  return (
    <View style={styles.container}>
      {productsLoading ? (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={{
            height: Dimensions.get('window').height / 1.5,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        />
      ) : (
        <FlatList
          contentContainerStyle={{
            paddingTop: 20,
            paddingHorizontal: 28,
            paddingBottom: 90,
          }}
          showsVerticalScrollIndicator={false}
          data={products}
          renderItem={({item}) => (
            <AdminStoreItem
              heading={item.productTitle}
              title={item.productLabel}
              imageProp={item.image}
              btnText="Borrowed"
              onPressRemove={() => {
                setSelectedProduct(item);
                setIsVisible(true);
              }}
              onPressEdit={() =>
                navigation.navigate('AddProducts', {
                  id: item.id,
                })
              }
            />
          )}
          keyExtractor={item => item.id}
          ListFooterComponent={ListFooterComponent}
        />
      )}
      <DeleteConfimationModal
        isVisible={isVisible}
        onClose={() => setIsVisible(false)}
        onPress={() => deleteProduct()}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    // paddingHorizontal: 18,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5, // Android shadow
    // marginHorizontal: 28,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },

  chartLabel: {
    fontSize: 18,
    fontFamily: fonts.SemiBold,
    color: '#ce8668',
  },
  chartBackground: {
    backgroundColor: colors.white, // Background color for the graph area
    borderRadius: 16,
    paddingHorizontal: 0,
    paddingVertical: 0,
  },

  monthlyContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    paddingTop: 22,
    paddingBottom: 30,
    paddingHorizontal: 20,
    marginTop: 24,
    overflow: 'hidden',
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
});

export default Analytics;

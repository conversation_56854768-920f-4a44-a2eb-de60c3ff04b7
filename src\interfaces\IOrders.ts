import {Timestamp} from '@react-native-firebase/firestore';
import {IProducts} from './Products';

export interface IOrders {
  orderId: string;
  addedAt: Timestamp;
  orderStatus: string;
  totalPrice: number;
  userId: string;
  productIds: IOrderProductDetails[];
  orderTitle: string;
  products?: IEnrichedProductDetails[]; // Array to store enriched product details
}
export interface IOrderProductDetails {
  createdAt: Timestamp;
  status: string;
  productId: string;
  quantity: string;
}

export interface IEnrichedProductDetails {
  productId: string;
  status: string;
  productTitle: string;
  productLabel: string;
  image: string;
}

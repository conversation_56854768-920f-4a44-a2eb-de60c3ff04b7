import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import {colors, fonts} from '../utilities/theme';
import {DollarIcon} from '../assets/svg';

interface Props {
  borrowSuccessModal: boolean;
  setBorrowSuccessModal: (val: boolean) => void;
  onPressOk: () => void;
  onBackdropPress: () => void;
  productTitle: string;
}

const BorrowSuccessModal: React.FC<Props> = ({
  borrowSuccessModal,
  setBorrowSuccessModal,
  onPressOk,
  onBackdropPress,
  productTitle,
}) => {
  return (
    <Modal isVisible={borrowSuccessModal} onBackdropPress={onBackdropPress}>
      <View style={styles.modalContainer}>
        <DollarIcon />
        <Text style={[styles.text, {marginTop: 12}]}>
          Thanks. You have added
        </Text>
        <Text style={styles.text}>
          <Text style={styles.highlight}>{productTitle}</Text> to the Borrowing
          Cart
        </Text>
        <TouchableOpacity style={styles.button} onPress={onPressOk}>
          <Text style={styles.buttonText}>OK</Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

export default BorrowSuccessModal;

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
  },
  text: {
    fontSize: 14,
    fontFamily: fonts.SemiBold,
    color: colors.black,
    textAlign: 'center',
  },
  highlight: {
    color: colors.primary,
  },
  button: {
    borderRadius: 4,
    backgroundColor: colors.primary,
    height: 26,
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    marginTop: 26,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontFamily: fonts.SemiBold,
    fontSize: 14,
  },
});

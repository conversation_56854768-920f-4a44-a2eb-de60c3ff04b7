import {
  StyleSheet,
  View,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Text,
  useWindowDimensions,
  Keyboard,
  ActivityIndicator,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {colors, fonts} from '../../../utilities/theme';
import {ChatHeader} from '../../../component';
import {
  Bubble,
  GiftedChat,
  InputToolbar,
  Send,
  Time,
  IMessage,
  Actions,
} from 'react-native-gifted-chat';
import {MicrophoneIcon, PlusIcon, SendIcon} from '../../../assets/svg';
import {IUser} from '../../../interfaces/IUser';
import firestore from '@react-native-firebase/firestore';
import UseChat from '../../../Hooks/UseChat';
import {useUser} from '../../../Hooks/UseContext';
import ImagePicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import storage from '@react-native-firebase/storage';
import ImagePickerModal from '../../../model/ImagePickerModal';

type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'ChatDetails'
>;
const ChatDetails: React.FC<Props> = ({navigation, route}) => {
  const {getAllMessages, messages, onSend, messageLoading} = UseChat();
  const {recipientUser} = route.params;
  const {user} = useUser();
  const {width} = useWindowDimensions();
  const [imageLocalPath, setImageLocalPath] = useState<string>('');
  const [imageUploading, setImageUploading] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    if (recipientUser?.userId) {
      getAllMessages(recipientUser.userId);
    }
  }, []);
  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };

  const handleUploadImage = async (imagePath: string): Promise<string> => {
    try {
      if (!user?.userId) throw new Error('User ID is undefined');

      const filePath = `Users/${user.userId}/chatImages/${
        user.userId
      }_img_${Date.now()}`; // Unique filename
      const reference = storage().ref(filePath);

      await reference.putFile(imagePath);

      return await reference.getDownloadURL();
    } catch (error) {
      console.error('Image upload failed:', error);
      throw error;
    }
  };

  const handlePickImage = () => {
    ImagePicker.openPicker({
      width: 300,
      height: 400,
      cropping: true,
      maxFiles: 1,
      mediaType: 'photo',
    }).then(image => {
      toggleModal();
      setImageLocalPath(image.path);
    });
  };

  const handleCameraImage = () => {
    ImagePicker.openCamera({
      width: 300,
      height: 400,
      cropping: true,
      mediaType: 'photo',
    }).then(image => {
      toggleModal();
      setImageLocalPath(image.path);
    });
  };

  const onSendMessage = async (messages: IMessage[] = []) => {
    // Make sure user is signed in

    try {
      const [messageToSend] = messages;

      if (imageLocalPath) {
        setImageUploading(true);
        const imageUrl = await handleUploadImage(imageLocalPath);
        setImageUploading(false);
        const newMessage = {
          ...messageToSend,
          image: imageUrl,
        };
        recipientUser?.userId && onSend([newMessage], recipientUser.userId);
        setImageLocalPath('');
      } else {
        recipientUser?.userId && onSend(messages, recipientUser?.userId);
      }
    } catch (error) {
      setImageUploading(false);
      console.log('erroor while seding medsssage', error);
    }
  };

  const renderChatFooter = useCallback(() => {
    if (imageLocalPath) {
      return (
        <View style={styles.chatFooter}>
          <Image
            resizeMode="contain"
            source={{uri: imageLocalPath || ''}}
            style={{height: 75, width: 75}}
          />
          {imageUploading ? (
            <Text style={{color: colors.primary, alignSelf: 'center'}}>
              Uploading...
            </Text>
          ) : null}
          <TouchableOpacity
            onPress={() => setImageLocalPath('')}
            style={styles.buttonFooterChatImg}>
            <Text style={styles.textFooterChat}>X</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return null;
  }, [imageLocalPath, imageUploading]);

  return (
    <View style={styles.container}>
      <SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
        {messageLoading ? (
          <ActivityIndicator
            color={colors.primary}
            size={'large'}
            style={{marginTop: 100}}
          />
        ) : (
          <>
            <ChatHeader
              onPress={() => {
                Keyboard.dismiss();
                setTimeout(() => {
                  navigation.goBack();
                }, 500);
              }}
              name={recipientUser?.name}
              image={recipientUser?.profileImage}
              userType={recipientUser.userType || 'User'}
            />
            <GiftedChat
              inverted={messages?.length == 0 ? false : true}
              messages={messages}
              onSend={messages => onSendMessage(messages)}
              user={{
                _id: user?.userId || '',
              }}
              alwaysShowSend
              showUserAvatar={false}
              showAvatarForEveryMessage={false}
              placeholder="Write now…"
              renderAvatarOnTop={false}
              messagesContainerStyle={[
                messages?.length == 0 && {transform: [{scaleY: -1}]},
                styles.chatsContainer,
              ]}
              // fix for image modal get blank
              lightboxProps={{
                activeProps: {
                  style: {
                    flex: 1,
                    resizeMode: 'contain',
                    width: width,
                  },
                },
              }}
              listViewProps={{
                showsVerticalScrollIndicator: false,
              }}
              textInputProps={{
                color: colors.black,
              }}
              renderChatFooter={renderChatFooter}
              renderBubble={props => {
                return (
                  <Bubble
                    {...props}
                    textStyle={{
                      left: styles.leftTextStyle,
                      right: styles.rightTextStyle,
                    }}
                    wrapperStyle={{
                      right: styles.rightWrapperStyle,
                      left: styles.leftWrapperStyle,
                    }}
                  />
                );
              }}
              renderSend={props => (
                <Send
                  {...props}
                  disabled={!props.text}
                  containerStyle={styles.containerStyle}>
                  <TouchableOpacity
                    disabled={!props.text && !imageLocalPath}
                    onPress={() => {
                      const trimmedText = props.text?.trim() ?? '';
                      if (props.onSend) {
                        props.onSend({text: trimmedText}, true);
                      }
                    }}
                    hitSlop={4}
                    style={styles.sendIcon}>
                    <SendIcon
                      fill={
                        !props.text && !imageLocalPath ? '#BB542780' : '#BB5427'
                      }
                    />
                  </TouchableOpacity>
                </Send>
              )}
              keyboardShouldPersistTaps={'never'}
              renderInputToolbar={props => (
                <View style={styles.bottomContainer}>
                  <View style={styles.inputIcons}>
                    {/* <TouchableOpacity style={{marginLeft: 5,}}>
                  <MicrophoneIcon />
                </TouchableOpacity> */}
                    <TouchableOpacity
                      onPress={() => {
                        Keyboard.dismiss();
                        setTimeout(() => {
                          toggleModal();
                        }, 300);
                      }}>
                      <PlusIcon />
                    </TouchableOpacity>
                  </View>
                  <InputToolbar
                    {...props}
                    containerStyle={styles.inputToolbarContainer}
                  />
                </View>
              )}
              renderTime={props => (
                <Time
                  {...props}
                  timeTextStyle={{
                    left: styles.timeLeftTextStyle,
                    right: styles.timeRightTextStyle,
                  }}
                />
              )}
            />
          </>
        )}
      </SafeAreaView>
      <ImagePickerModal
        isVisible={isModalVisible}
        onClose={toggleModal}
        onCameraPress={handleCameraImage}
        onGalleryPress={handlePickImage}
      />
    </View>
  );
};

export default ChatDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    // paddingHorizontal: 30,
  },
  chatsContainer: {
    backgroundColor: '#f9f9f8',
    borderTopLeftRadius: 50,
    borderTopRightRadius: 50,
    paddingHorizontal: 30,
    // marginBottom: 10
  },
  leftTextStyle: {
    color: colors.black,
    fontSize: 14,
    fontFamily: fonts.Regular,
  },
  rightTextStyle: {
    color: colors.primary,
    fontSize: 14,
    fontFamily: fonts.Regular,
  },
  rightWrapperStyle: {
    backgroundColor: '#BB542710',
    borderRadius: 10,
    marginBottom: 22,
    paddingHorizontal: 8,
    paddingVertical: 6,
  },
  leftWrapperStyle: {
    backgroundColor: '#768CAA29',
    borderRadius: 10,
    marginBottom: 32,
    paddingHorizontal: 8,
    paddingVertical: 6,
    marginLeft: -45,
  },

  timeLeftTextStyle: {
    color: colors.gray[50],
    textAlign: 'left',
    position: 'absolute',
    top: 16,
    left: -18,
  },
  timeRightTextStyle: {
    color: colors.gray[50],
    textAlign: 'right',
    position: 'absolute',
    top: 14,
    right: -18,
  },

  bottomContainer: {
    paddingHorizontal: 30,
    paddingBottom: 10,
    flexDirection: 'row',
    backgroundColor: '#f9f9f8',
  },
  containerStyle: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  inputToolbarContainer: {
    backgroundColor: '#f5efee',
    borderColor: '#f5efee',
    borderRadius: 16,
    flexDirection: 'row',
    paddingTop: 2,
    paddingBottom: 2,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    borderTopColor: '#f5efee',
  },
  inputIcons: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 5,
  },
  sendIcon: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chatFooter: {
    flexDirection: 'row',
    marginBottom: 20,
    marginHorizontal: 20,
    borderRadius: 8,
    overflow: 'hidden',
    justifyContent: 'space-between',
    backgroundColor: `${colors.primary}20`,
  },
  buttonFooterChatImg: {
    backgroundColor: colors.gray,
    margin: 8,
    height: 32,
    width: 32,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },

  textFooterChat: {
    color: colors.primary,
  },
});

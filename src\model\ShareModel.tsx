import React from 'react';
import {Text, StyleSheet, View, ViewStyle} from 'react-native';
import Modal from 'react-native-modal';
import {AppButton} from '../component';
import {colors, fonts} from '../utilities/theme';
import {FacebookIcon, InstagramIcon, WhatsappIcon} from '../assets/svg';
import {IProducts} from '../interfaces/Products';
import Share from 'react-native-share';
import RNFS from 'react-native-fs';

interface Props {
  isVisible: boolean;
  onClose: () => void;
  onPress?: () => void;
  product?: IProducts;
}

const ShareModal: React.FC<Props> = ({
  isVisible,
  onClose,
  onPress,
  product,
}) => {
  const shareOnFacebook = async () => {
    if (!product) return;
    try {
      const shareOptions = {
        title: `Check out ${product.productTitle}!`,
        message: `${product.productTitle}: ${product.description}`,
        url: product.image,
        social: Share.Social.FACEBOOK,
        appId: 'hello',
      };
      await Share.shareSingle(shareOptions);
    } catch (error) {
      console.error('Error sharing to Facebook:', error);
    }
  };

  const shareOnWhatsApp = async () => {
    if (!product) return;
    try {
      const shareOptions = {
        title: `Check out ${product.productTitle}!`,
        message: `${product.productTitle}: ${product.description}`,
        url: product.image,
        social: Share.Social.WHATSAPP,
      };
      await Share.shareSingle(shareOptions);
    } catch (error) {
      console.error('Error sharing to Instagram:', error);
    }
  };

  const shareOnInstagram = async () => {
    if (!product) return;

    try {
      const localImagePath = `${RNFS.DocumentDirectoryPath}/instagram_story_image.jpg`;
      if (!product.image) {
        console.error('Invalid image URL');
        return;
      }
      const downloadResult = await RNFS.downloadFile({
        fromUrl: product.image,
        toFile: localImagePath,
      }).promise;

      if (downloadResult.statusCode === 200) {
        const shareOptions = {
          method: Share.Social.INSTAGRAM_STORIES,
          backgroundImage: `file://${localImagePath}`,
          social: Share.Social.INSTAGRAM_STORIES,
          appId: 'your_fb_app_id',
        };
        await Share.shareSingle(shareOptions);
      } else {
        console.error('Failed to download image');
      }
    } catch (error) {
      console.error('Error sharing to Instagram:', error);
    }
  };

  return (
    <Modal
      isVisible={isVisible}
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      onSwipeComplete={onClose}
      backdropOpacity={0.1}
      swipeDirection={'down'}
      onBackdropPress={onClose}
      style={{justifyContent: 'flex-end', margin: 0}}>
      <View style={styles.modalContainer}>
        <Text style={styles.title}>Share on</Text>
        <AppButton
          title="Instagram"
          customStyle={styles.customStyle}
          titleStyle={styles.titleStyle}
          onPress={shareOnInstagram}
          removeEdit={true}
          icon={<InstagramIcon />}
        />

        <AppButton
          title="facebook"
          customStyle={styles.customStyle}
          titleStyle={styles.titleStyle}
          onPress={shareOnFacebook}
          removeEdit={true}
          icon={<FacebookIcon />}
        />

        <AppButton
          title="whatsapp"
          customStyle={[styles.customStyle, {marginBottom: 15}] as ViewStyle}
          titleStyle={styles.titleStyle}
          onPress={shareOnWhatsApp}
          removeEdit={true}
          icon={<WhatsappIcon />}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },

  title: {
    fontSize: 16,
    marginTop: 15,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  customStyle: {
    width: '100%',
    marginTop: 10,
    backgroundColor: `${colors.blue}15`,
  },

  titleStyle: {
    color: colors.blue,
    fontSize: 14,
    letterSpacing: 2,
    fontFamily: fonts.SemiBold,
    textTransform: 'uppercase',
    alignSelf: 'center',
  },
});

export default ShareModal;

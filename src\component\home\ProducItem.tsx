import {
  StyleSheet,
  View,
  Text,
  ViewStyle,
  TextInputProps,
  Image,
  TouchableOpacity,
} from 'react-native';
import React, {useState} from 'react';
import {appStyles, colors, fonts} from '../../utilities/theme';
import {FavoriteIcon, StarIcon, UnFavoriteIcon} from '../../assets/svg';
import AppButton from '../common/AppButton';

interface Props {
  productTitle: string;
  productLabel: string;
  price: number;
  imageProp: string;
  ratingStar: number;
  ratingReview: number;
  containerStyle?: ViewStyle;
  onPress?: () => void;
  onPressBarrow?: () => void;
  onPressPurchase?: () => void;
  onPressFavorite?: () => void;
  isFavorite?: boolean;
  borrowLoading?: boolean;
  purchaseLoading?: boolean;
}

const ProductItem: React.FC<Props> = ({
  productTitle,
  productLabel,
  price,
  imageProp,
  ratingStar,
  ratingReview,
  onPress,
  onPressBarrow,
  borrowLoading,
  onPressPurchase,
  purchaseLoading,
  onPressFavorite,
  isFavorite,
  containerStyle,
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.9}
      style={[styles.container, containerStyle]}
      onPress={onPress}>
      <Image
        source={{
          uri: imageProp,
        }}
        resizeMode="cover"
        style={styles.imgStyle}
      />
      <View style={{paddingHorizontal: 15, paddingBottom: 15}}>
        <View style={styles.textContainer}>
          <View
            style={{
              flexDirection: 'column',
              flex: 1,
              marginRight: 10,
            }}>
            <Text style={[appStyles.h3, {color: colors.primary}]}>
              {productTitle}
            </Text>
            <Text style={[appStyles.body6, {color: colors.gray[50]}]}>
              {productLabel}
            </Text>
          </View>
          <TouchableOpacity onPress={onPressFavorite}>
            {isFavorite ? <FavoriteIcon /> : <UnFavoriteIcon />}
          </TouchableOpacity>
        </View>
        <View style={styles.priceContainer}>
          <View style={{flexDirection: 'row'}}>
            <StarIcon />
            <Text style={styles.ratingText}>
              {typeof ratingStar === 'number' && !isNaN(ratingStar)
                ? ratingStar.toFixed(1)
                : '0.0'}
            </Text>
            <Text style={styles.reviewText}>{ratingReview} Reviews</Text>
          </View>
          <Text style={[appStyles.h1, {color: colors.primary}]}>
            $NZ{price}
          </Text>
        </View>
        <View style={styles.buttonContainer}>
          <AppButton
            title="Borrow"
            customStyle={{width: '48%', backgroundColor: colors.black}}
            titleStyle={styles.buttonText}
            onPress={onPressBarrow}
            isLoading={borrowLoading}
          />
          <AppButton
            title="Purchase"
            customStyle={{width: '48%'}}
            titleStyle={styles.buttonText}
            onPress={onPressPurchase}
            isLoading={purchaseLoading}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ProductItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 20,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    overflow: 'hidden',
  },
  imgStyle: {
    height: 202,
    width: '100%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
  },
  priceContainer: {
    marginTop: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reviewText: {
    fontSize: 12,
    fontFamily: fonts.Medium,
    color: colors.gray[200],
    marginLeft: 11,
  },
  ratingText: {fontSize: 12, fontFamily: fonts.Bold, marginLeft: 7},
  buttonText: {fontSize: 14, fontFamily: fonts.SemiBold},
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
});

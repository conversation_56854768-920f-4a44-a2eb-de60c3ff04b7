import {
  Image,
  StyleSheet,
  Text,
  View,
  TextInputProps,
  ImageSourcePropType,
  ViewStyle,
} from 'react-native';
import React from 'react';
import AppButton from '../common/AppButton';
import {colors, fonts} from '../../utilities/theme';
import Navigation from '../../navigation/Navigation';
import {AdminEditIcon, AdminRemoveIcon} from '../../assets/svg';
interface Props extends TextInputProps {
  heading?: string;
  title?: string;
  cost?: string;
  date?: string;
  btnText?: string;
  onPressTrack?: () => void;
  imageProp?: string;
  onPress?: () => void;
  btnShow?: boolean;
}
const AdminManagementItems: React.FC<Props> = ({
  heading,
  title,
  cost,
  date,
  btnText,
  imageProp,
  onPress,
  onPressTrack,
  btnShow = false,
}) => (
  <View>
    <View style={styles.itemContainer}>
      <View style={{flexDirection: 'row'}}>
        <View style={{flex: 1, marginRight: 10}}>
          <AppButton
            title={btnText}
            titleStyle={styles.btnText}
            customStyle={
              [
                styles.btnContainer,
                {
                  backgroundColor:
                    btnText === 'Borrowed' ? colors.black : colors.primary,
                },
              ] as ViewStyle
            }
          />

          <Text style={styles.headingText}>{heading}</Text>
          <Text style={[styles.titleText, {color: colors.gray[50]}]}>
            {title}
          </Text>
        </View>
        <View style={{flex: 0.5}}>
          <Image
            style={{height: 90, width: '100%'}}
            source={{uri: imageProp}} // Use the imageProp as the uri
            resizeMode="contain"
          />
        </View>
      </View>
      <View style={styles.sendCostContainer}>
        <AppButton
          title="Send Reminder"
          titleStyle={styles.btnText}
          customStyle={
            [
              styles.sendBtnContainer,
              {backgroundColor: colors.yellow},
            ] as ViewStyle
          }
        />
        <Text style={styles.dateStyle}>{date}</Text>
      </View>
    </View>
  </View>
);

export default AdminManagementItems;
const styles = StyleSheet.create({
  itemContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    marginTop: 2,
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    marginHorizontal: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },

  headingText: {
    marginTop: 5,
    fontSize: 18,
    fontFamily: fonts.Bold,
    color: colors.black,
  },

  titleText: {
    fontSize: 12,
    fontWeight: '300',
  },

  btnContainer: {
    // flex: 1,
    height: 31,
    width: 90,
    // paddingHorizontal: 17,
    // paddingVertical: 8,
    borderRadius: 15,
  },

  sendBtnContainer: {
    height: 31,
    borderRadius: 15,
    paddingHorizontal: 17,
  },

  btnText: {
    fontFamily: fonts.Medium,
    fontSize: 9,
  },

  costDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  costStyle: {
    fontSize: 20,
    fontFamily: fonts.Bold,
    color: colors.black,
  },

  dateStyle: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },

  sendCostContainer: {
    marginTop: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

import {
  StyleSheet,
  Text,
  TextStyle,
  ActivityIndicator,
  TouchableOpacity,
  ViewStyle,
  View,
} from 'react-native';
import React from 'react';
import {colors, fonts} from '../../utilities/theme';
import {EditIcon} from '../../assets/svg';
const AppButton = ({
  title,
  customStyle,
  titleStyle,
  onPress,
  isLoading,
  disabled,
  removeEdit,
  icon,
}: {
  title?: string;
  customStyle?: ViewStyle;
  titleStyle?: TextStyle;
  onPress?: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  removeEdit?: boolean;
  icon?: React.ReactNode;
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      style={[
        styles.container,
        {backgroundColor: disabled ? '#D4D3D3' : colors.primary},
        customStyle,
      ]}>
      {isLoading ? (
        <ActivityIndicator color={'white'} size={'small'} />
      ) : (
        <View style={{flexDirection: 'row'}}>
          {icon && removeEdit && (
            <View style={{marginRight: 2, alignSelf: 'center'}}>{icon}</View>
          )}
          <Text style={[styles.title, titleStyle]}> {title}</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

export default AppButton;
const styles = StyleSheet.create({
  container: {
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    height: 54,
  },
  title: {
    fontSize: 18,
    lineHeight: 25,
    color: colors.white,
    fontFamily: fonts.SemiBold,
  },
});

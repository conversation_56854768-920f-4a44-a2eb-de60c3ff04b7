import {
  StyleSheet,
  View,
  Text,
  FlatList,
  SafeAreaView,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthHeader} from '../../../component';
import {colors, fonts} from '../../../utilities/theme';
import {
  NotificationCheckIcon,
  NotificationUnCheckIcon,
} from '../../../assets/svg';
import MyCartItem from '../../../component/mycart/MyCartItem';
import {ORDER_DETAILS} from '../../../constants';
import firestore, {Timestamp} from '@react-native-firebase/firestore';
import {IProducts} from '../../../interfaces/Products';
import {useUser} from '../../../Hooks/UseContext';
import {formatDistanceToNow} from 'date-fns';
import {IOrderProductDetails, IOrders} from '../../../interfaces/IOrders';
import CustomHeader from '../../../component/common/CustomHeader';

type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'OrderTracking'
>;
const OrderTracking: React.FC<Props> = ({navigation, route}) => {
  const {productIds, status, addedAt, orderId} = route.params || {};
  const {user} = useUser();
  const [products, setProducts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [orderDetails, setOrderDetails] = useState(ORDER_DETAILS);

  const calculateTimeAgo = (date: Date): string => {
    return formatDistanceToNow(date, {addSuffix: true});
  };
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        if (!orderId || !productIds || productIds.length === 0) {
          console.log('No product IDs provided');
          setProducts([]);
          setIsLoading(false);
          return;
        }
        const orderSnapshot = await firestore()
          .collection('Orders')
          .doc(orderId)
          .get();
        const orderData = orderSnapshot.data();
        if (!orderData || !orderData.productIds) {
          console.log('No product details found in the order');
          setProducts([]);
          return;
        }
        const productDetailsFromOrders: IOrderProductDetails[] =
          orderData.productIds;

        const productDocs = await firestore()
          .collection('Products')
          .where(firestore.FieldPath.documentId(), 'in', productIds)
          .get();

        const productData = productDocs.docs.map(doc => ({
          ...doc.data(),
          id: doc.id,
        }));
        const mergedProducts = productDetailsFromOrders.map(orderProduct => {
          let createdAt = orderProduct.createdAt;
          const productDetails =
            productData.find(
              product => product.id === orderProduct.productId,
            ) || {};

          return {
            ...orderProduct,
            ...productDetails,
            createdAt,
          };
        });
        setProducts(mergedProducts);
      } catch (error) {
        console.error('Error fetching product details:', error);
        setProducts([]);
      } finally {
        setIsLoading(false);
      }
    };
    fetchProducts();
  }, [orderId, productIds]);

  const updateOrderDetails = (status: string, addedAt: string) => {
    const updatedOrderDetails = ORDER_DETAILS.map(item => {
      if (status === 'Pending' && item.id === '1') {
        item.checked = true;
        item.time = addedAt ? addedAt : '';
      } else if (status === 'Processing' && item.id === '2') {
        item.checked = true;
        item.time = addedAt ? addedAt : '';
      } else if (status === 'Delivered' && item.id === '3') {
        item.checked = true;
        item.time = addedAt ? addedAt : '';
      } else if (status === 'Delivered' && item.id === '4') {
        item.checked = true;
        item.time = addedAt ? addedAt : '';
      } else {
        item.checked = false;
        item.time = '';
      }
      return item;
    });
    setOrderDetails(updatedOrderDetails);
  };

  useEffect(() => {
    if (status && addedAt) {
      updateOrderDetails(status, addedAt);
    }
  }, [status, addedAt]);

  const OrderLimitFooter = () => {
    return (
      <FlatList
        showsVerticalScrollIndicator={false}
        data={orderDetails}
        keyExtractor={item => item.id}
        renderItem={({item}) => (
          <View style={styles.orderContainer}>
            <View style={styles.checkOrderContainer}>
              <View style={{flexDirection: 'row', flex: 1}}>
                <View>
                  {item.checked ? (
                    <NotificationCheckIcon />
                  ) : (
                    <NotificationUnCheckIcon />
                  )}
                </View>
                <Text
                  style={[
                    styles.checkOrderText,
                    {color: item.checked ? colors.primary : '#8F8E8E'},
                  ]}>
                  {item.checkText}
                </Text>
              </View>
              <Text style={styles.orderLabel}>{item.time}</Text>
            </View>
            <Text style={styles.orderHeading}>{item.heading}</Text>
            <Text style={styles.orderLabel}>{item.title}</Text>
          </View>
        )}
      />
    );
  };

  return (
    <View style={styles.container}>
      <SafeAreaView>
        <View style={{marginTop: 25}} />
        {isLoading ? (
          <ActivityIndicator
            size="large"
            color={colors.primary}
            style={{
              height: Dimensions.get('window').height / 1.5,
            }}
          />
        ) : (
          <FlatList
            showsVerticalScrollIndicator={false}
            style={{paddingHorizontal: 32}}
            keyExtractor={(_, index) => index.toString()}
            data={products}
            renderItem={({item}) => {
              return (
                <MyCartItem
                  heading={item.productTitle}
                  title={item.productLabel}
                  imageProp={item.image}
                  cost={item.price}
                  btnShow={true}
                  date={calculateTimeAgo(
                    item.createdAt instanceof Timestamp
                      ? item.createdAt.toDate()
                      : item.createdAt,
                  )}
                  btnText={item.status}
                />
              );
            }}
            ListFooterComponent={OrderLimitFooter}
          />
        )}
      </SafeAreaView>
    </View>
  );
};

export default OrderTracking;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  orderContainer: {
    height: 126,
    paddingHorizontal: 17,
    paddingTop: 15,
    backgroundColor: colors.white,
    borderRadius: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 2,
  },

  checkOrderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  inActionCheckIcon: {
    width: 20,
    height: 20,
    backgroundColor: '#D4D3D3',
    borderRadius: 6,
  },

  checkOrderText: {
    fontSize: 11,
    fontFamily: fonts.Regular,
    marginLeft: 5,
    alignSelf: 'center',
    // color: colors.primary,
  },

  orderHeading: {
    fontSize: 16,
    fontFamily: fonts.Bold,
    color: colors.black,
    marginVertical: 5,
  },

  orderLabel: {
    fontSize: 10,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },
});

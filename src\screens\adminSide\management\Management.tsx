import {
  StyleSheet,
  View,
  Text,
  FlatList,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AdminBottomTabParamlist} from '../../../navigation/AdminBottomTabs';
import {colors} from '../../../utilities/theme';
import AdminManagementItems from '../../../component/adminCommon/AdminManagementItems';
import firestore, {Timestamp} from '@react-native-firebase/firestore';
import {AdminStackParamsList} from '../../../navigation/AdminNavigation';
import {formatDistanceToNow} from 'date-fns';
import {IEnrichedProductDetails, IOrders} from '../../../interfaces/IOrders';

type Props = NativeStackScreenProps<
  AdminBottomTabParamlist & AdminStackParamsList,
  'Management'
>;

const Management: React.FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [orders, setOrders] = useState<IOrders[]>([]);

  const calculateTimeAgo = (date: Timestamp) => {
    if (!date) return;
    return formatDistanceToNow(date.toDate(), {addSuffix: true});
  };

  useEffect(() => {
    setIsLoading(true);
    const unsubscribe = firestore()
      .collection('Orders')
      .onSnapshot(async snapshot => {
        const updatedOrders: IOrders[] = [];

        for (const orderDoc of snapshot.docs) {
          const orderData = orderDoc.data() as IOrders;
          if (
            updatedOrders.some(order => order.orderId === orderData.orderId)
          ) {
            continue;
          }

          const productsData: IEnrichedProductDetails[] = [];
          if (orderData.productIds) {
            for (const {productId, status} of orderData.productIds) {
              const productDoc = await firestore()
                .collection('Products')
                .doc(productId)
                .get();
              const productData = productDoc.exists ? productDoc.data() : {};
              productsData.push({
                productId,
                status,
                productTitle: productData?.productTitle || 'Unknown',
                productLabel: productData?.productLabel || 'Unknown',
                image: productData?.image || '',
              });
            }
          }
          updatedOrders.push({
            ...orderData,
            products: productsData,
          });
        }
        setOrders(updatedOrders);
        setIsLoading(false);
      });
    return unsubscribe;
  }, []);

  return (
    <View style={styles.container}>
      {isLoading ? (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={{
            height: Dimensions.get('window').height / 1.5,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        />
      ) : orders.length === 0 ? (
        <View
          style={{
            height: Dimensions.get('window').height / 1.5,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text style={{fontSize: 14, color: '#666', textAlign: 'center'}}>
            No orders placed yet. Please check back later.
          </Text>
        </View>
      ) : (
        <FlatList
          contentContainerStyle={{
            paddingTop: 20,
            paddingHorizontal: 28,
            paddingBottom: 10,
          }}
          showsVerticalScrollIndicator={false}
          data={orders}
          keyExtractor={(item, index) => `${item.orderId}-${index}`} // Unique key for each order
          renderItem={({item}) => (
            <View>
              {item.products &&
                item.products.length > 0 &&
                item.products.map((product, productIndex) => {
                  // Unique key for each product based on orderId, productId, and index
                  const uniqueProductKey = `${item.orderId}-${product.productId}-${productIndex}`;

                  return (
                    <AdminManagementItems
                      key={uniqueProductKey}
                      heading={product.productTitle}
                      title={product.productLabel}
                      imageProp={product.image}
                      date={calculateTimeAgo(item.addedAt)}
                      btnText={product.status}
                    />
                  );
                })}
            </View>
          )}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
});

export default Management;

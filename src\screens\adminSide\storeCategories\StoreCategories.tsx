import {
  StyleSheet,
  View,
  FlatList,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AdminBottomTabParamlist} from '../../../navigation/AdminBottomTabs';
import {AppButton} from '../../../component';
import firestore from '@react-native-firebase/firestore';
import {colors} from '../../../utilities/theme';
import CategoriesItems from './CategoriesItems';
import {ICategories} from '../../../interfaces/Products';
import DeleteConfimationModal from '../../../model/DeleteConfimationModal';
import {AdminStackParamsList} from '../../../navigation/AdminNavigation';
type Props = NativeStackScreenProps<
  AdminBottomTabParamlist & AdminStackParamsList,
  'StoreCategories'
>;

const StoreCategories: React.FC<Props> = ({navigation}) => {
  const [categories, setCategories] = useState<ICategories[]>([]);
  const [categoryLoading, setCategoryLoading] = useState<boolean>(false);

  const [selectedCategory, setSelectedCategory] = useState<ICategories | null>(
    null,
  );
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setCategoryLoading(true);
    const unsubscribe = firestore()
      .collection('Categories')
      .orderBy('updatedAt', 'desc')
      .onSnapshot(
        snapshot => {
          const _categories = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
          })) as ICategories[];
          setCategories(_categories);
          setCategoryLoading(false);
        },
        error => {
          console.error('Error fetching categories:', error);
          setCategoryLoading(false);
        },
      );
    return () => unsubscribe();
  }, []);

  const deleteCategory = () => {
    if (selectedCategory) {
      firestore()
        .collection('Categories')
        .doc(selectedCategory.id)
        .delete()
        .then(() => {
          setCategories(prevCategories =>
            prevCategories.filter(item => item.id !== selectedCategory.id),
          );
          setIsVisible(false);
        })
        .catch(error => {
          console.error('Error deleting category: ', error);
        });
    }
  };

  return (
    <View style={styles.container}>
      {categoryLoading ? (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={{
            height: Dimensions.get('window').height / 1.5,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        />
      ) : (
        <FlatList
          contentContainerStyle={{
            paddingTop: 20,
            paddingHorizontal: 28,
            paddingBottom: 24,
          }}
          showsVerticalScrollIndicator={false}
          data={categories}
          renderItem={({item}) => (
            <CategoriesItems
              categoryLabel={item.categoryLabel}
              onPressRemove={() => {
                setSelectedCategory(item);
                setIsVisible(true);
              }}
              onPressEdit={() =>
                navigation.navigate('AddCategories', {
                  id: item.id,
                })
              }
            />
          )}
          keyExtractor={item => item.id}
        />
      )}

      <View style={styles.bottomContainer}>
        <AppButton
          title="Add New Category"
          onPress={() => navigation.navigate('AddCategories')}
        />
      </View>
      <DeleteConfimationModal
        isVisible={isVisible}
        onClose={() => setIsVisible(false)}
        onPress={() => deleteCategory()}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    backgroundColor: colors.white,
    paddingHorizontal: 28,
    paddingVertical: 16,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    elevation: 5,
    shadowColor: '#000',
    shadowOpacity: 0.3,
    shadowRadius: 15,
    shadowOffset: {width: 0, height: -6},
  },
});

export default StoreCategories;

import {
  StyleSheet,
  View,
  FlatList,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AdminBottomTabParamlist} from '../../../navigation/AdminBottomTabs';
import {AppButton} from '../../../component';
import {colors} from '../../../utilities/theme';
import AdminStoreItem from '../../../component/adminCommon/AdminStoreItem';
import {IProducts} from '../../../interfaces/Products';
import firestore from '@react-native-firebase/firestore';
import {AdminStackParamsList} from '../../../navigation/AdminNavigation';
import DeleteConfimationModal from '../../../model/DeleteConfimationModal';
type Props = NativeStackScreenProps<
  AdminBottomTabParamlist & AdminStackParamsList,
  'StoreProducts'
>;

const StoreProducts: React.FC<Props> = ({navigation}) => {
  const [products, setProducts] = useState<IProducts[]>([]);
  const [productsLoading, setProductsLoading] = useState(false);

  const [selectedProduct, setSelectedProduct] = useState<IProducts | null>(
    null,
  );
  const [isVisible, setIsVisible] = useState(false);
  useEffect(() => {
    setProductsLoading(true);
    const unsubscribe = firestore()
      .collection('Products')
      .orderBy('updatedAt', 'desc')
      .onSnapshot(
        snapshot => {
          const _products = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
          })) as IProducts[];
          setProducts(_products);
          setProductsLoading(false);
        },
        error => {
          console.log('Error while fetching all products', error);
          setProductsLoading(false);
        },
      );
    return () => unsubscribe();
  }, []);

  const deleteProduct = () => {
    if (selectedProduct) {
      firestore()
        .collection('Products')
        .doc(selectedProduct.id)
        .delete()
        .then(() => {
          setProducts(prevProducts =>
            prevProducts.filter(item => item.id !== selectedProduct.id),
          );
          setIsVisible(false);
        })
        .catch(error => {
          console.error('Error deleting product: ', error);
        });
    }
  };
  return (
    <View style={styles.container}>
      {productsLoading ? (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={{
            height: Dimensions.get('window').height / 1.5,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        />
      ) : (
        <FlatList
          contentContainerStyle={{
            paddingTop: 20,
            paddingHorizontal: 28,
            paddingBottom: 90,
          }}
          showsVerticalScrollIndicator={false}
          data={products}
          renderItem={({item}) => (
            <AdminStoreItem
              heading={item.productTitle}
              title={item.productLabel}
              imageProp={item.image}
              btnText="Borrowed"
              onPressRemove={() => {
                setSelectedProduct(item);
                setIsVisible(true);
              }}
              onPressEdit={() =>
                navigation.navigate('AddProducts', {
                  id: item.id,
                })
              }
            />
          )}
          keyExtractor={item => item.id}
        />
      )}
      <View style={styles.bottomContainer}>
        <AppButton
          title="Add New Product"
          onPress={() => navigation.navigate('AddProducts')}
        />
      </View>
      <DeleteConfimationModal
        isVisible={isVisible}
        onClose={() => setIsVisible(false)}
        onPress={() => deleteProduct()}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    backgroundColor: colors.white,
    paddingHorizontal: 28,
    paddingVertical: 16,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    elevation: 5,
    shadowColor: '#000',
    shadowOpacity: 0.3,
    shadowRadius: 15,
    shadowOffset: {width: 0, height: -6},
  },
});

export default StoreProducts;

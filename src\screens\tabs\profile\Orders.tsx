import {
  StyleSheet,
  View,
  Text,
  FlatList,
  SafeAreaView,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import {AuthHeader} from '../../../component';
import {colors} from '../../../utilities/theme';
import {useUser} from '../../../Hooks/UseContext';
import firestore, {Timestamp} from '@react-native-firebase/firestore';
import {formatDistanceToNow} from 'date-fns';
import {IOrders} from '../../../interfaces/IOrders';
import OrderItems from '../../../component/profile/OrderItems';
import CustomHeader from '../../../component/common/CustomHeader';

type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'Orders'
>;

const Orders: React.FC<Props> = ({navigation, route}) => {
  const [orders, setOrders] = useState<IOrders[]>([]);
  const {user} = useUser();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsLoading(true);
    const unsubscribeUser = firestore()
      .collection('Users')
      .doc(user?.userId)
      .onSnapshot(docSnapshot => {
        const userData = docSnapshot.data();
        const orderIds = userData?.myOrders || [];

        if (orderIds.length === 0) {
          setOrders([]);
          setIsLoading(false);
          return;
        }
        firestore()
          .collection('Orders')
          .where(firestore.FieldPath.documentId(), 'in', orderIds)
          .get()
          .then(querySnapshot => {
            const orders: IOrders[] = querySnapshot.docs
              .map(doc => doc.data() as IOrders)
              .sort((a, b) => b.addedAt.toMillis() - a.addedAt.toMillis());
            setOrders(orders);
            setIsLoading(false);
          })
          .catch(error => {
            console.error('Error fetching orders:', error);
            setIsLoading(false);
          });
      });
    return () => {
      unsubscribeUser();
    };
  }, [user?.userId]);

  const checkProductInOrders = async (item: IOrders) => {
    const productIds = item.productIds?.map(product => product.productId) || [];
    const timeAgo = formatDistanceToNow(item.addedAt.toDate(), {
      addSuffix: true,
    });
    navigation.navigate('OrderTracking', {
      productIds: productIds,
      status: item.orderStatus,
      addedAt: timeAgo,
      orderId: item.orderId,
    });
  };
  return (
    <View style={styles.container}>
      <SafeAreaView />

      <View style={{paddingHorizontal: 32, marginBottom: 16, marginTop: 28}}>
        <SafeAreaView />
      </View>
      {isLoading ? (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={{
            height: Dimensions.get('window').height / 1.5,
          }}
        />
      ) : orders.length === 0 ? (
        <Text style={styles.emptyMessage}>
          No orders yet?{'\n'} Start shopping and track everything in one place!
        </Text>
      ) : (
        <FlatList
          keyExtractor={item => item.orderId}
          showsVerticalScrollIndicator={false}
          style={{paddingHorizontal: 32}}
          data={orders}
          renderItem={({item, index}) => {
            return (
              <OrderItems
                heading={item.orderTitle}
                count={index + 1}
                btnText={item.orderStatus}
                date={formatDistanceToNow(item.addedAt.toDate(), {
                  addSuffix: true,
                })}
                cost={item.totalPrice}
                onPressCard={() => checkProductInOrders(item)}
              />
            );
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyMessage: {
    marginHorizontal: 32,
    textAlign: 'center',
    marginTop: Dimensions.get('window').height / 3.5,
    fontSize: 16,
    color: colors.gray[50],
  },

  orderContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: colors.white,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    marginBottom: 20,
    marginTop: 5,
  },
  orderId: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.black,
  },
  totalAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
  },
  dateCreated: {
    fontSize: 14,
    color: colors.gray[200],
  },
  status: {
    fontSize: 14,
    color: colors.gray[150],
  },
  productCount: {
    fontSize: 14,
    color: colors.gray[150],
  },
  footerContainer: {
    padding: 16,
    backgroundColor: colors.white,
  },
  cartValue: {
    position: 'absolute',
    zIndex: 1,
    top: -5,
    right: -4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 15,
    width: 18,
    height: 18,
  },
});

export default Orders;

import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import {appStyles, colors, fonts} from '../../utilities/theme';
import {StarIcon} from '../../assets/svg';
import {images} from '../../assets/images';

interface Props {
  url: string;
  name: string;
  rating: number;
  reviewText: string;
}

const RatingItem: React.FC<Props> = ({url, name, rating, reviewText}) => {
  return (
    <View style={styles.container}>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <Image
          source={url ? {uri: url} : images.Avatar}
          style={styles.imgStyle}
        />
        <View style={{justifyContent: 'space-between', flexDirection: 'row'}}>
          <View
            style={{
              flexDirection: 'column',
              marginLeft: 10,
              width: '60%',
            }}>
            <Text style={appStyles.h4}>{name}</Text>
            <Text style={[appStyles.body6, {color: colors.primary}]}>
              Hockey Player
            </Text>
          </View>
          <View style={{flexDirection: 'row'}}>
            <StarIcon />
            <Text style={styles.ratingText}>{rating.toFixed(1)}</Text>
          </View>
        </View>
      </View>
      <Text style={styles.subtitle}>{reviewText}</Text>
    </View>
  );
};

export default RatingItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 10,
    paddingVertical: 15,
    marginHorizontal: 27,
    marginBottom: 15,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  imgStyle: {
    width: 50,
    height: 50,
    borderRadius: 100,
  },
  ratingText: {
    fontSize: 12,
    fontFamily: fonts.Bold,
    marginLeft: 7,
    color: colors.black,
  },
  subtitle: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    color: colors.gray[250],
    marginTop: 20,
  },
});

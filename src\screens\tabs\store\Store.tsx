import {
  StyleSheet,
  View,
  Text,
  FlatList,
  SafeAreaView,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import {<PERSON>pp<PERSON><PERSON>on, AuthHeader} from '../../../component';
import {colors, fonts} from '../../../utilities/theme';
import MyCartItem from '../../../component/mycart/MyCartItem';
import {useUser} from '../../../Hooks/UseContext';
import {IProducts} from '../../../interfaces/Products';
import firestore from '@react-native-firebase/firestore';
import {formatDistanceToNow} from 'date-fns';
import {showToast} from '../../../helper/toast';
import {IOrders} from '../../../interfaces/IOrders';
import {ICart, ICartItem} from '../../../interfaces/ICart';

type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'Store'
>;

const Store: React.FC<Props> = ({navigation}) => {
  const [totalCart, setTotalCarts] = useState(0);
  const {user} = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [myCart, setMyCart] = useState<ICart[]>([]);
  const [checkoutLoading, setCheckoutLoading] = useState(false);

  const calculateTimeAgo = (date: Date): string => {
    return formatDistanceToNow(date, {addSuffix: true});
  };

  useEffect(() => {
    if (!user) return;
    setIsLoading(true);
    const unsubscribe = firestore()
      .collection('Users')
      .doc(user.userId)
      .onSnapshot(async doc => {
        const userData = doc.data();
        if (!userData?.myCart || userData.myCart.length === 0) {
          setIsLoading(false);
          setMyCart([]);
          return;
        }
        const cartItems: ICartItem[] = userData.myCart;
        const productIds = cartItems.map(item => item.productId);
        try {
          const productsSnapshot = await firestore()
            .collection('Products')
            .where(firestore.FieldPath.documentId(), 'in', productIds)
            .get();

          const updatedCart: ICart[] = cartItems.map(cartItem => {
            const productData = productsSnapshot.docs
              .find(doc => doc.id === cartItem.productId)
              ?.data() as IProducts;
            return {
              productData,
              myCart: [
                {
                  productId: cartItem.productId,
                  status: cartItem.status,
                  createdAt: cartItem.createdAt,
                  quantity: cartItem.quantity,
                },
              ],
              timeAgo: calculateTimeAgo(cartItem.createdAt.toDate()),
            };
          });
          setMyCart(updatedCart);
          setTotalCarts(cartItems.length);
        } catch (error) {
          console.error('Error fetching product data:', error);
        } finally {
          setIsLoading(false);
        }
      });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setMyCart(prevCart => {
        const updatedCart = prevCart.map(item => {
          const latestCreatedAt = item.myCart.reduce((latest, cart) => {
            const cartDate = cart.createdAt?.toDate() || new Date();
            return cartDate > latest ? cartDate : latest;
          }, new Date(0));
          const updatedTimeAgo = calculateTimeAgo(latestCreatedAt);
          return {
            ...item,
            timeAgo: updatedTimeAgo,
          };
        });
        return updatedCart;
      });
    }, 60000);
    return () => clearInterval(intervalId);
  }, []);

  const handleDeleteCartItem = async (productId: string) => {
    if (!user) return;
    try {
      const userRef = firestore().collection('Users').doc(user.userId);
      const userSnapshot = await userRef.get();
      if (userSnapshot.exists) {
        const userData = userSnapshot.data() as ICart;
        const cartItemToRemove = userData.myCart.find(
          item => item.productId === productId,
        );

        await userRef.update({
          myCart: firestore.FieldValue.arrayRemove(cartItemToRemove),
        });
        showToast(
          `Product removed from cart successfully!`,
          'success',
          'success',
        );
      }
    } catch (error) {
      console.error('Error removing cart item:', error);
    }
  };

  const createOrder = async () => {
    if (!user) return;
    setCheckoutLoading(true);
    try {
      let totalPrice = 0;
      let orderTitle = '';
      let productIds: ICartItem[] = [];
      for (const cart of myCart) {
        for (const item of cart.myCart) {
          const product = cart.productData;
          if (product?.price) {
            if (!product || product.quantity == null || product.quantity <= 0) {
              showToast(`Product ${product.productTitle} is out of stock!`);
              return;
            }
            totalPrice += product.price;
            orderTitle += orderTitle ? ' & ' : '';
            orderTitle += product.productTitle;
            productIds.push({
              productId: item.productId,
              status: item.status,
              createdAt: item.createdAt,
              quantity: item.quantity,
            });
            const productRef = firestore()
              .collection('Products')
              .doc(item.productId);

            await productRef.update({
              quantity: firestore.FieldValue.increment(-1),
            });
          }
        }
      }
      const orderData: IOrders = {
        addedAt: firestore.Timestamp.now(),
        orderStatus: 'Pending',
        totalPrice,
        userId: user.userId,
        productIds,
        orderTitle,
        orderId: '',
      };

      const orderRef = firestore().collection('Orders').doc();
      orderData.orderId = orderRef.id;
      await orderRef.set(orderData);
      await firestore()
        .collection('Users')
        .doc(user.userId)
        .update({
          myOrders: firestore.FieldValue.arrayUnion(orderRef.id),
          myCart: [],
        });
      navigation.navigate('CongratsProfile', {
        title: 'Card has been added Successfully',
      });
    } catch (error) {
      console.error('Error creating order:', error);
    } finally {
      setCheckoutLoading(false);
    }
  };

  const ListFooterComponent = ({totalCart}: {totalCart: number}) => {
    return (
      <View style={{paddingBottom: 80}}>
        {totalCart >= 2 && (
          <View style={styles.orderLimitContainer}>
            <Text style={styles.orderHeading}>Order Limit Reached</Text>
            <Text style={styles.orderText}>
              You can’t have more than 2 products borrowed or purchased.
            </Text>
          </View>
        )}
        <AppButton
          title="Proceed to checkout"
          onPress={createOrder}
          isLoading={checkoutLoading}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <SafeAreaView />

      {isLoading ? (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={{
            height: Dimensions.get('window').height / 1.5,
          }}
        />
      ) : myCart.length === 0 ? (
        <Text style={styles.emptyMessage}>
          Your cart is waiting! Add your favorites and get ready to shop.
        </Text>
      ) : (
        <FlatList
          contentContainerStyle={{paddingHorizontal: 32, paddingBottom: 24}}
          showsVerticalScrollIndicator={false}
          data={myCart}
          renderItem={({item}) => {
            return (
              <>
                {item.myCart.map((cartItem, index) => (
                  <MyCartItem
                    key={index}
                    btnShowClose={true}
                    heading={item.productData?.productTitle}
                    title={item.productData?.productLabel}
                    imageProp={item.productData?.image}
                    cost={item.productData?.price ?? 0}
                    date={item.timeAgo}
                    btnText={cartItem.status}
                    btnShow={true}
                    onPressClose={() =>
                      handleDeleteCartItem(cartItem.productId || '')
                    }
                  />
                ))}
              </>
            );
          }}
          ListFooterComponent={<ListFooterComponent totalCart={totalCart} />}
          keyExtractor={item => item.productData?.id || ''}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  orderLimitContainer: {
    height: 100,
    backgroundColor: colors.white,
    paddingTop: 15,
    paddingHorizontal: 17,
    borderRadius: 20,
    marginBottom: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 2,
  },

  orderHeading: {
    fontSize: 18,
    fontFamily: fonts.Bold,
    color: colors.black,
  },

  orderText: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
    textAlign: 'center',
    width: '80%',
  },
  emptyMessage: {
    marginHorizontal: 32,
    textAlign: 'center',
    marginTop: Dimensions.get('window').height / 3.3,
    fontSize: 16,
    color: colors.gray[50],
  },
});

export default Store;

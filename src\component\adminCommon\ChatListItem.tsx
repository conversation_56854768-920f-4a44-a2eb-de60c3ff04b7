import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {IChat} from '../../interfaces/IChat';
import {IUser} from '../../interfaces/IUser';
import {images} from '../../assets/images';
import {useUser} from '../../Hooks/UseContext';
import {format, isToday, isYesterday} from 'date-fns';
import firestore, {Timestamp} from '@react-native-firebase/firestore';
import {colors, fonts} from '../../utilities/theme';

interface Props {
  item: IChat;
  onPress: (recipientId: Partial<IUser>) => void;
}

const ChatListItem: React.FC<Props> = ({item, onPress}) => {
  const {user} = useUser();
  const [recipientData, setRecipientData] = useState<Partial<IUser>>();

  useEffect(() => {
    const recipientId = item.members.find((id: string) => id !== user?.userId);
    if (recipientId) {
      fetchRecipient(recipientId);
    }
  }, [item]);

  const fetchRecipient = async (id: string) => {
    const userDoc = await firestore().collection('Users').doc(id).get();
    const userData = userDoc.data();
    if (userData) {
      setRecipientData({...userData});
    }
  };

  const formatMessageTime = (createdAt: Date | string | Timestamp): string => {
    const date =
      createdAt instanceof Timestamp ? createdAt.toDate() : new Date(createdAt);
    if (isToday(date)) return format(date, 'hh:mm a');
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'dd/MM/yy');
  };

  return (
    <TouchableOpacity
      style={styles.chatContainer}
      onPress={() => recipientData && onPress(recipientData)}>
      <View style={styles.imageWrapper}>
        <Image
          style={styles.profileImage}
          source={
            recipientData?.profileImage
              ? {uri: recipientData.profileImage}
              : images.Avatar
          }
        />
        {item.lastMessage.isOnline && <View style={styles.onlineProfileDot} />}
      </View>

      <View style={styles.messageContent}>
        <View style={styles.row}>
          <Text style={styles.nameText} numberOfLines={1}>
            {recipientData?.name}
          </Text>
          <Text style={styles.timeAgoText}>
            {item.lastMessage?.createdAt &&
              formatMessageTime(item.lastMessage.createdAt)}
          </Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.lastMessageText} numberOfLines={1}>
            {item.lastMessage?.text || ''}
          </Text>
          {/* {item.unreadMessages > 0 && (
          <View style={styles.unreadMessagesContainer}>
            <Text style={styles.unreadMessagesText}>{item.unreadMessages}</Text>
          </View>
            )} */}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ChatListItem;

const styles = StyleSheet.create({
  chatContainer: {
    backgroundColor: colors.white,
    flexDirection: 'row',
    borderRadius: 16,
    marginBottom: 12,
    paddingHorizontal: 18,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    marginHorizontal: 4,
  },
  imageWrapper: {
    position: 'relative',
    marginRight: 10,
  },
  profileImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  onlineProfileDot: {
    position: 'absolute',
    bottom: 4,
    right: -4,
    width: 14,
    height: 14,
    borderRadius: 11,
    backgroundColor: '#2ECC71',
    borderColor: colors.white,
    borderWidth: 2,
  },
  messageContent: {
    flex: 1,
    justifyContent: 'space-evenly',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nameText: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.Medium,
    flex: 1,
  },
  timeAgoText: {
    fontSize: 10,
    color: colors.gray[250],
    fontFamily: fonts.Medium,
  },
  lastMessageText: {
    fontSize: 13,
    color: colors.gray[250],
    fontFamily: fonts.Regular,
    flex: 1,
    marginRight: 20,
  },
  unreadMessagesContainer: {
    width: 20,
    height: 20,
    borderRadius: 10, // Make it circular
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unreadMessagesText: {
    fontSize: 12,
    color: colors.white,
    fontFamily: fonts.SemiBold,
    textAlign: 'center',
  },
});

import React, {useState} from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../navigation/AuthNavigation';
import {appStyles, colors, fonts} from '../../utilities/theme';
import {
  AppButton,
  BottomLine,
  FBLoginButton,
  FormInput,
  GoogleLoginButton,
} from '../../component';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {useUser} from '../../Hooks/UseContext';

import useAuth from '../../Hooks/UseAuth';
import {LoginButton} from 'react-native-fbsdk-next';
import {GoogleSigninButton} from '@react-native-google-signin/google-signin';

type Props = NativeStackScreenProps<AuthStackParamList, 'SignIn'>;

const SignIn: React.FC<Props> = ({navigation}) => {
  const {setUser} = useUser();
  const {
    signIn,
    signInWithFacebook,
    signInWithGoogle,
    loading,
    googleLoginLoading,
    facebookLoginLoading,
  } = useAuth();
  const [hidePassword, setHidePassword] = useState(true);
  const togglePassword = () => setHidePassword(!hidePassword);
  const [isChecked, setIsChecked] = useState(false);

  const validationSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email').required('Email is required'),
    password: Yup.string().required('Password is required'),
  });

  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },

    validationSchema: validationSchema,
    onSubmit: values => {
      const {email, password} = values;
      signIn(email, password);
    },
  });

  return (
    <KeyboardAvoidingView
      style={{flex: 1}}
      behavior={Platform.OS === 'android' ? 'height' : 'padding'}>
      <View style={styles.container}>
        <ScrollView
          contentContainerStyle={styles.contentContainerStyle}
          showsVerticalScrollIndicator={false}>
          <View>
            <FormInput
              placeholder="Enter email or phone number"
              containerStyle={{marginTop: 10}}
              keyboardType="email-address"
              onChangeText={formik.handleChange('email')}
              value={formik.values.email}
              onBlur={formik.handleBlur('email')}
              errorMessage={formik.touched.email && formik.errors.email}
            />
            <FormInput
              placeholder="Password"
              isPassword={true}
              secureTextEntry={hidePassword}
              onLeftIconPress={togglePassword}
              onChangeText={formik.handleChange('password')}
              value={formik.values.password}
              onBlur={formik.handleBlur('password')}
              errorMessage={formik.touched.password && formik.errors.password}
            />
            <TouchableOpacity
              onPress={() => navigation.navigate('ForgetPassword')}>
              <Text style={[appStyles.body5, styles.forgetPassword]}>
                Forget Password?
              </Text>
            </TouchableOpacity>
            {/* <View style={styles.rememberContainer}>
                <TouchableOpacity onPress={() => setIsChecked(!isChecked)}>
                  {isChecked ? <CheckIcon /> : <UnCheckIcon />}
                </TouchableOpacity>
                <Text
                  style={[
                    styles.rememberText,
                    { color: !isChecked ? colors.black : colors.primary },
                  ]}>
                  Remember Password
                </Text>
              </View> */}

            <AppButton
              title="Login"
              customStyle={{marginTop: 22}}
              onPress={formik.handleSubmit}
              isLoading={loading}
              disabled={!(formik.isValid && formik.dirty)}
            />

            <Text
              style={
                (appStyles.body5,
                {
                  fontFamily: fonts.Medium,
                  textAlign: 'center',
                  marginVertical: 22,
                })
              }>
              OR
            </Text>
            <GoogleLoginButton
              onPress={signInWithGoogle}
              loading={googleLoginLoading}
            />
            <FBLoginButton
              onPress={signInWithFacebook}
              loading={facebookLoginLoading}
            />
          </View>

          <BottomLine
            onPress={() => navigation.navigate('Register')}
            subtitle="Don’t have an account? Let’s "
            title="Register"
          />
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

export default SignIn;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 24,
    paddingBottom: 16,
  },

  rememberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 30,
  },
  rememberText: {
    fontSize: 14,
    fontFamily: fonts.Regular,
    marginLeft: 8,
  },

  forgetPassword: {color: colors.primary, textAlign: 'right', marginTop: 10},
  googleButton: {
    backgroundColor: `${colors.primary}28`,
    height: 48,
  },
  facebookButton: {
    backgroundColor: `${colors.blue}28`,
    height: 48,
    marginTop: 22,
  },
  contentContainerStyle: {
    flexGrow: 1,
    justifyContent: 'space-between',
    marginHorizontal: 4,
    paddingBottom: 16,
  },
});

import {StyleSheet, TouchableOpacity} from 'react-native';
import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {appStyles, colors, fonts} from '../utilities/theme';
import BottomTabs from './BottomNavigation';
import ContactUs from '../screens/tabs/profile/ContactUs';
import EditProfile from '../screens/tabs/profile/EditProfile';
import {ChatDetails, ProductDetails} from '../screens/tabs';
import Notification from '../screens/tabs/search/Notification';
import Options from '../screens/tabs/profile/Options';
import AddBank from '../screens/tabs/profile/AddBank';
import SelectBank from '../screens/tabs/profile/SelectBank';
import AddCard from '../screens/tabs/profile/AddCard';
import AddWallet from '../screens/tabs/profile/AddWallet';
import SelectWallet from '../screens/tabs/profile/SelectWallet';
import CongratsProfile from '../component/profile/CongratsProfile';
import OrderTracking from '../screens/tabs/profile/OrderTracking';
import MyCart from '../screens/tabs/search/MyCart';
import Orders from '../screens/tabs/profile/Orders';
import {BackIcon} from '../assets/svg';
import {IUser} from '../interfaces/IUser';

export type HomeStackParamsList = {
  BottomTabs: undefined;
  ContactUs: undefined;
  EditProfile: undefined;
  Notification: undefined;
  MyCart: undefined;
  ProductDetails: {
    id: string;
    productTitle: string;
    productLabel: string;
    category: string;
    description: string;
    price: number;
    ratingReview: number;
    ratingStar: number;
    image: string;
    wishList?: string[];
  };
  Options: undefined;
  Orders: undefined;
  // {shouldPopToTop?: boolean};
  AddBank: undefined;
  SelectBank: undefined;
  ChatDetails: {recipientUser: Partial<IUser>};
  AddCard: {productId?: (string | {id?: string})[]};
  AddWallet: undefined;
  SelectWallet: undefined;
  CongratsProfile: {title: string};
  OrderTracking?: {
    orderId?: string;
    productIds?: (string | {id: string})[];
    status?: string;
    addedAt?: string;
  };
};

const HomeStackNavigator = () => {
  const HomeStack = createNativeStackNavigator<HomeStackParamsList>();

  return (
    <HomeStack.Navigator
      screenOptions={({navigation}) => ({
        headerShown: false,
        headerShadowVisible: false,
        headerTitleAlign: 'center',
        // headerStyle: styles.containerStyle,
        headerTitleStyle: appStyles.headerTitleStyle,
        headerLeft: () => {
          return (
            <TouchableOpacity
              style={appStyles.iconContainer}
              activeOpacity={0.7}
              onPress={() => navigation.goBack()}>
              <BackIcon />
            </TouchableOpacity>
          );
        },
      })}>
      <HomeStack.Screen name="BottomTabs" component={BottomTabs} />
      <HomeStack.Screen
        name="ContactUs"
        component={ContactUs}
        options={{headerTitle: 'Contact Us', headerShown: true}}
      />
      <HomeStack.Screen
        name="EditProfile"
        component={EditProfile}
        options={{headerTitle: 'Edit Profile', headerShown: true}}
      />
      <HomeStack.Screen
        name="MyCart"
        component={MyCart}
        options={{headerTitle: 'My Cart', headerShown: true}}
      />
      <HomeStack.Screen
        name="Notification"
        component={Notification}
        options={{headerTitle: 'Notifications', headerShown: true}}
      />
      <HomeStack.Screen name="ProductDetails" component={ProductDetails} />
      <HomeStack.Screen name="CongratsProfile" component={CongratsProfile} />
      <HomeStack.Screen
        name="Options"
        component={Options}
        options={{headerTitle: 'Options', headerShown: true}}
      />
      <HomeStack.Screen name="AddBank" component={AddBank} />
      <HomeStack.Screen name="SelectBank" component={SelectBank} />
      <HomeStack.Screen name="ChatDetails" component={ChatDetails} />
      <HomeStack.Screen
        name="AddCard"
        component={AddCard}
        options={{headerTitle: 'Add new card', headerShown: true}}
      />
      <HomeStack.Screen name="AddWallet" component={AddWallet} />
      <HomeStack.Screen name="SelectWallet" component={SelectWallet} />
      <HomeStack.Screen
        name="OrderTracking"
        component={OrderTracking}
        options={{headerTitle: 'Order Tracking', headerShown: true}}
      />
      <HomeStack.Screen
        name="Orders"
        component={Orders}
        options={{headerTitle: 'My Orders', headerShown: true}}
      />
    </HomeStack.Navigator>
  );
};

export default HomeStackNavigator;

const styles = StyleSheet.create({
  hitSlop: {
    left: 10,
    right: 10,
    bottom: 10,
    top: 10,
  },
  titleStyle: {
    color: colors.black,
    fontSize: 16,
    fontFamily: fonts.Bold,
    lineHeight: 28,
  },
  containerStyle: {
    // backgroundColor: colors.bgcolor,
  },
});

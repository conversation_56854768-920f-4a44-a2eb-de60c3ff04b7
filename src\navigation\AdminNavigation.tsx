import {StyleSheet, TouchableOpacity} from 'react-native';
import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {appStyles, colors, fonts} from '../utilities/theme';
import AdminBottomTabs from './AdminBottomTabs';
import AddProducts from '../screens/adminSide/storeProducts/AddProducts';
import EditProfile from '../screens/adminSide/profile/EditProfile';
import AddCategories from '../screens/adminSide/storeCategories/AddCategories';
import ShowCategoriesModal from '../screens/adminSide/storeProducts/ShowCategoriesModal';
import ChangePassword from '../screens/adminSide/profile/ChangePassword';
import UserListChat from '../screens/adminSide/profile/UserListChat';
import {BackIcon} from '../assets/svg';
import {ChatDetails} from '../screens/tabs';
import {IUser} from '../interfaces/IUser';
export type AdminStackParamsList = {
  AdminBottomTabs: undefined;
  AddProducts?: {
    id?: string;
  };
  AddCategories?: {
    id?: string;
  };
  EditProfile: undefined;
  ShowCategoriesModal: undefined;
  ChangePassword: undefined;
  UserListChat: undefined;
  ChatDetails: {recipientUser: Partial<IUser>};
};

const AdminStackNavigator = () => {
  const AdminStack = createNativeStackNavigator<AdminStackParamsList>();

  return (
    <AdminStack.Navigator
      screenOptions={({navigation}) => ({
        headerShown: false,
        headerShadowVisible: false,
        headerTitleAlign: 'center',
        headerStyle: styles.containerStyle,
        headerTitleStyle: appStyles.headerTitleStyle,
        headerLeft: () => {
          return (
            <TouchableOpacity
              style={[appStyles.iconContainer, {marginLeft: 16}]}
              activeOpacity={0.7}
              onPress={() => navigation.goBack()}>
              <BackIcon />
            </TouchableOpacity>
          );
        },
      })}>
      <AdminStack.Screen name="AdminBottomTabs" component={AdminBottomTabs} />
      <AdminStack.Screen
        name="AddProducts"
        component={AddProducts}
        options={{headerShown: true, headerTitle: 'Add Products'}}
      />
      <AdminStack.Screen
        name="EditProfile"
        component={EditProfile}
        options={{headerShown: true, headerTitle: 'Edit Profile'}}
      />
      <AdminStack.Screen
        name="AddCategories"
        component={AddCategories}
        options={{headerShown: true}}
      />
      <AdminStack.Screen
        name="ShowCategoriesModal"
        component={ShowCategoriesModal}
        options={{
          presentation: 'modal',
        }}
      />
      <AdminStack.Screen
        name="ChangePassword"
        component={ChangePassword}
        options={{headerShown: true, headerTitle: 'Change Password'}}
      />
      <AdminStack.Screen
        name="UserListChat"
        component={UserListChat}
        options={{headerShown: true, headerTitle: 'Chats'}}
      />
      <AdminStack.Screen
        name="ChatDetails"
        component={ChatDetails}
        options={{headerShown: false}}
      />
    </AdminStack.Navigator>
  );
};

export default AdminStackNavigator;

const styles = StyleSheet.create({
  hitSlop: {
    left: 10,
    right: 10,
    bottom: 10,
    top: 10,
  },
  titleStyle: {
    color: colors.black,
    fontSize: 16,
    fontFamily: fonts.Bold,
    lineHeight: 28,
  },
  containerStyle: {
    backgroundColor: colors.bgcolor,
  },
});

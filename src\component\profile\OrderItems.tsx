import {
  Image,
  StyleSheet,
  Text,
  View,
  TextInputProps,
  ImageSourcePropType,
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import AppButton from '../common/AppButton';
import {appStyles, colors, fonts} from '../../utilities/theme';
import Navigation from '../../navigation/Navigation';
interface Props extends TextInputProps {
  heading?: string;
  count?: number;
  cost?: number | string;
  date?: string;
  btnText?: string;
  onPressCard?: () => void;
}
const OrderItems: React.FC<Props> = ({
  heading,
  count,
  btnText,
  date,
  cost,
  onPressCard,
}) => (
  <TouchableOpacity onPress={onPressCard} style={styles.itemContainer}>
    <View style={{flex: 1, marginRight: 10}}>
      <Text style={[appStyles.body6, {fontSize: 11}]} numberOfLines={1}>
        {heading}
      </Text>
      <Text style={[styles.titleText]}>Order: {count}</Text>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <Text
          style={[
            styles.statusStyle,
            {
              backgroundColor:
                btnText === 'Pending'
                  ? '#FABC2C'
                  : btnText === 'Processing'
                  ? '#3995DB'
                  : btnText === 'On The Way'
                  ? '#FC5D20'
                  : btnText === 'Delivered'
                  ? '#3BB537'
                  : '#000',
            },
          ]}>
          {btnText}
        </Text>
        <Text style={styles.dateStyle}>{date}</Text>
      </View>
    </View>

    <Text style={styles.headingText}>$NZ{cost}</Text>
  </TouchableOpacity>
);

export default OrderItems;
const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 8,
    marginTop: 2,
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginHorizontal: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },

  titleText: {
    fontSize: 9,
    fontFamily: fonts.Medium,
    color: colors.black,
    marginVertical: 5,
  },

  headingText: {
    fontSize: 12,
    fontFamily: fonts.Bold,
    color: colors.primary,
    marginVertical: 10,
    alignSelf: 'center',
  },
  statusStyle: {
    fontSize: 9,
    fontFamily: fonts.Regular,
    paddingHorizontal: 12,
    color: colors.white,
    backgroundColor: 'orange',
    paddingVertical: 2,
    borderRadius: 2,
    marginRight: 6,
  },

  btnText: {
    fontFamily: fonts.Medium,
    fontSize: 9,
  },

  dateStyle: {
    fontSize: 8,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },
});

import React from 'react';
import {StyleSheet, View, Text, Image, SafeAreaView} from 'react-native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../navigation/BottomNavigation';
import {colors, fonts} from '../../utilities/theme';
import {CongratsImg} from '../../assets/svg';
import {AppButton} from '..';
import {AuthStackParamList} from '../../navigation/AuthNavigation';
import {images} from '../../assets/images';
type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList & AuthStackParamList,
  'CongratsProfile'
>;

// type Props = NativeStackScreenProps<AuthStackParamList, 'Congrats'>;

const CongratsProfile: React.FC<Props> = ({route, navigation}) => {
  const {title} = route.params;
  return (
    <View style={styles.container}>
      <SafeAreaView />
      <Image
        style={styles.voittoLogoStyle}
        source={images.voittoLogo}
        resizeMode="contain"
      />
      <CongratsImg style={styles.iconStyle} />
      <Text style={styles.titleStyle}>Congrats!</Text>
      <Text style={styles.textStyle}>{title}</Text>

      {title === 'Password Change Successfully' && (
        <AppButton
          title="Continue"
          customStyle={styles.containerStyle}
          onPress={() => navigation.navigate('SignIn')}
        />
      )}

      {title === 'Account Created Successfully' && (
        <AppButton
          title="Continue"
          customStyle={styles.containerStyle}
          onPress={() => navigation.navigate('SignIn')}
        />
      )}

      {title === 'Card has been added Successfully' && (
        <AppButton
          title="Continue"
          customStyle={styles.containerStyle}
          onPress={() => navigation.replace('Orders')}
        />
      )}

      {title === 'Message has been sent Successfully' && (
        <AppButton
          title="Continue"
          customStyle={styles.containerStyle}
          onPress={() =>
            navigation.reset({
              index: 0,
              routes: [
                {name: 'BottomTabs', state: {routes: [{name: 'Profile'}]}},
              ],
            })
          }
        />
      )}

      {![
        'Password Change Successfully',
        'Account Created Successfully',
        'Card has been added Successfully',
      ].includes(title) && (
        <AppButton
          title="Continue"
          customStyle={styles.containerStyle}
          onPress={() => navigation.navigate('Options')}
        />
      )}
    </View>
  );
};

export default CongratsProfile;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 32,
  },

  voittoLogoStyle: {
    width: 250,
    height: 100,
    marginTop: 20,
    alignSelf: 'center',
  },

  iconStyle: {
    alignSelf: 'center',
    marginTop: 80,
  },

  titleStyle: {
    fontSize: 40,
    fontFamily: fonts.Bold,
    color: colors.black,
    alignSelf: 'center',
    marginTop: 35,
  },

  textStyle: {
    fontSize: 18,
    fontFamily: fonts.Regular,
    color: colors.blackLight,
    alignSelf: 'center',
    textAlign: 'center',
    width: '80%',
  },
  containerStyle: {width: '50%', alignSelf: 'center', marginTop: 80},
});

import React, {useState, useEffect} from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../navigation/AuthNavigation';
import {appStyles, colors, fonts} from '../../utilities/theme';
import {
  AppButton,
  BottomLine,
  FBLoginButton,
  FormInput,
  GoogleLoginButton,
} from '../../component';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {CheckIcon, UnCheckIcon} from '../../assets/svg';
import {useUser} from '../../Hooks/UseContext';
import {GoogleSignin} from '@react-native-google-signin/google-signin';
import useAuth from '../../Hooks/UseAuth';

type Props = NativeStackScreenProps<AuthStackParamList, 'Register'>;

const Register: React.FC<Props> = ({navigation}) => {
  const {setUser} = useUser();
  const {
    signUp,
    signInWithGoogle,
    signInWithFacebook,
    googleLoginLoading,
    facebookLoginLoading,
    loading,
  } = useAuth();
  const [hidePasswod, setHidePassword] = useState(true);
  const togglePassword = () => setHidePassword(!hidePasswod);
  const [isChecked, setIsChecked] = useState(false);
  const toggleCheckIcon = () => setIsChecked(!isChecked);

  const validationSchema = Yup.object().shape({
    name: Yup.string().required('Name is required'),
    email: Yup.string().email('Invalid email').required('Email is required'),
    password: Yup.string().required('Password is required'),
    term: Yup.boolean()
      .oneOf([true], 'You must accept the rememer password')
      .required('Remember password is required'),
  });

  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
      password: '',
      term: false,
    },

    validationSchema: validationSchema,
    onSubmit: async values => {
      const {name, email, password} = values;
      signUp(email, password, name);
    },
  });

  useEffect(() => {
    GoogleSignin.configure({
      scopes: ['email', 'profile'],
      webClientId:
        '1025338324164-kekj8gedcsntd66f3p25c802s7mbqtf2.apps.googleusercontent.com',
    });
  }, []);

  return (
    <KeyboardAvoidingView
      style={{flex: 1}}
      behavior={Platform.OS === 'android' ? 'height' : 'padding'}>
      <View style={styles.container}>
        <ScrollView
          contentContainerStyle={styles.mainContainer}
          showsVerticalScrollIndicator={false}>
          <View>
            <FormInput
              placeholder="Full Name"
              onChangeText={formik.handleChange('name')}
              value={formik.values.name}
              onBlur={formik.handleBlur('name')}
              errorMessage={formik.touched.name && formik.errors.name}
            />
            <FormInput
              placeholder="Enter email or phone number"
              keyboardType="email-address"
              onChangeText={formik.handleChange('email')}
              value={formik.values.email}
              onBlur={formik.handleBlur('email')}
              errorMessage={formik.touched.email && formik.errors.email}
            />
            <FormInput
              placeholder="Password"
              isPassword={true}
              secureTextEntry={hidePasswod}
              onLeftIconPress={togglePassword}
              onChangeText={formik.handleChange('password')}
              value={formik.values.password}
              onBlur={formik.handleBlur('password')}
              errorMessage={formik.touched.password && formik.errors.password}
            />

            <View style={styles.rememberContainer}>
              <TouchableOpacity
                onPress={() =>
                  formik.setFieldValue('term', !formik.values.term)
                }>
                {formik.values.term ? <CheckIcon /> : <UnCheckIcon />}
              </TouchableOpacity>
              <Text style={[styles.rememberText, ,]}>
                I agree with Terms & Conditions
              </Text>
            </View>

            <AppButton
              title="Register"
              customStyle={{marginTop: 22}}
              onPress={formik.handleSubmit}
              isLoading={loading}
              disabled={!(formik.isValid && formik.dirty)}
            />

            <Text
              style={
                (appStyles.body5,
                {
                  fontFamily: fonts.Medium,
                  textAlign: 'center',
                  marginVertical: 22,
                })
              }>
              OR
            </Text>
            <GoogleLoginButton
              onPress={signInWithGoogle}
              loading={googleLoginLoading}
            />
            <FBLoginButton
              onPress={signInWithFacebook}
              loading={facebookLoginLoading}
            />
          </View>
          <BottomLine
            title="Log In"
            subtitle="Already Registered? Let’s "
            onPress={() => navigation.navigate('SignIn')}
          />
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

export default Register;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 24,
    paddingBottom: 16,
  },
  mainContainer: {
    flexGrow: 1,
    justifyContent: 'space-between',
    marginHorizontal: 4,
    paddingBottom: 12,
  },
  rememberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 30,
  },

  rememberText: {
    fontSize: 14,
    fontFamily: fonts.Regular,
    marginLeft: 8,
    color: colors.primary,
  },

  googleButton: {
    backgroundColor: `${colors.primary}28`,
    height: 48,
  },
  facebookButton: {
    backgroundColor: `${colors.blue}28`,
    height: 48,
    marginTop: 22,
  },
});

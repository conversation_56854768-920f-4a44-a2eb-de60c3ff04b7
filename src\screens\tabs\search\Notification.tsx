import {StyleSheet, View, Text, FlatList, SafeAreaView} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import {colors, fonts} from '../../../utilities/theme';
import {NotificationCheckIcon} from '../../../assets/svg';
import {ORDER_DETAILS} from '../../../constants';
type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'Notification'
>;

const Notification: React.FC<Props> = ({navigation}) => {
  return (
    <View style={styles.container}>
      <SafeAreaView />

      <View style={{marginBottom: 17}} />
      <FlatList
        showsVerticalScrollIndicator={false}
        style={{paddingHorizontal: 32}}
        data={ORDER_DETAILS}
        renderItem={({item}) => (
          <View style={styles.orderContainer}>
            <View style={styles.checkOrderContainer}>
              <View style={{flexDirection: 'row', flex: 1}}>
                <NotificationCheckIcon />
                <Text style={styles.checkOrderText}>{item.checkText}</Text>
              </View>

              <Text style={styles.orderLabel}>{item.time}</Text>
            </View>
            <Text style={styles.orderHeading}>{item.heading}</Text>
            <Text style={styles.orderLabel}>{item.title}</Text>
          </View>
        )}
        keyExtractor={item => item.id}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // paddingTop: 18,
    backgroundColor: colors.white,
  },

  orderContainer: {
    height: 126,
    paddingHorizontal: 17,
    paddingTop: 15,
    backgroundColor: colors.white,
    borderRadius: 20,
    marginTop: 10,
    marginBottom: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },

  checkOrderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  inActionCheckIcon: {
    width: 20,
    height: 20,
    backgroundColor: '#D4D3D3',
    borderRadius: 6,
  },
  checkOrderText: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    marginLeft: 10,
    color: colors.primary,
  },

  orderHeading: {
    fontSize: 16,
    fontFamily: fonts.Bold,
    color: colors.black,
    marginVertical: 5,
  },

  orderLabel: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },
});

export default Notification;

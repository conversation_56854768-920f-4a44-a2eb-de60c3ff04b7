import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import AuthStackNavigator from './AuthNavigation';
import HomeStackNavigator from './HomeNavigation';
import AdminStackNavigator from './AdminNavigation';
import {useUser} from '../Hooks/UseContext';
import {MenuProvider} from 'react-native-popup-menu';
import {TourGuideProvider} from 'rn-tourguide';
import CustomToolTip from '../component/customToolTip/CustomToolTip';
import {StripeProvider} from '@stripe/stripe-react-native';
import {PUBLIC_KEY} from '@env';

const Navigation = () => {
  const {user} = useUser();

  return (
    <StripeProvider
      publishableKey={PUBLIC_KEY}
      merchantIdentifier="merchant.identifier" // required for Apple Pay
      urlScheme="your-url-scheme">
      <TourGuideProvider
        {...{borderRadius: 16}}
        tooltipComponent={CustomToolTip}
        labels={{
          skip: 'Skip',
          previous: 'Previous',
          next: 'Next',
          finish: 'Finish',
        }}>
        <MenuProvider>
          <NavigationContainer>
            {user?.userId ? (
              user.userType === 'Admin' ? (
                <AdminStackNavigator />
              ) : (
                <HomeStackNavigator />
              )
            ) : (
              <AuthStackNavigator />
            )}
          </NavigationContainer>
        </MenuProvider>
      </TourGuideProvider>
    </StripeProvider>
  );
};
export default Navigation;

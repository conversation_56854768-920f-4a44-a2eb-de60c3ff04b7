import {Platform, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import {colors, fonts} from '../utilities/theme';

interface Props {
  isModalVisible: boolean;
  setModalVisible: (val: boolean) => void;
  onCanclePress: () => void;
  onPressBorrow: () => void;
}

const BorrowTermsandCondition: React.FC<Props> = ({
  isModalVisible,
  setModalVisible,
  onCanclePress,
  onPressBorrow,
}) => {
  const TextItem = ({text, percent}: {text: string; percent?: string}) => {
    return (
      <View style={styles.textItemContainer}>
        <View style={styles.bulletPoint} />
        <Text style={styles.textItemText}>
          <Text style={styles.textItemBold}>{percent ? percent : ''}</Text>
          {text}
        </Text>
      </View>
    );
  };

  return (
    <Modal
      isVisible={isModalVisible}
      onBackdropPress={() => setModalVisible(false)}>
      <View style={styles.modalContainer}>
        <Text style={styles.modalTitle}>Terms & Conditions</Text>
        <TextItem
          percent="11% "
          text={
            'Deposit refundable when stick returned in the same condition it was sent.'
          }
        />
        <TextItem
          percent="00% "
          text={
            'Deposit retained by VOITTO if stick is badly damaged or broken and unfit for re-borrowing'
          }
        />
        <TextItem text={'Borrowing time frame'} />
        <TextItem text={'Free return and packaging'} />
        <TextItem text={'This transaction is built on trust'} />
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={onCanclePress}>
            <Text style={styles.buttonText}>Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={onPressBorrow}>
            <Text style={styles.buttonText}>Borrow</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default BorrowTermsandCondition;

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    color: colors.primary,
    fontFamily: fonts.SemiBold,
  },
  textItemContainer: {
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 6,
  },
  bulletPoint: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#BB5427',
    marginTop: Platform.OS === 'android' ? 4 : 3,
  },
  textItemText: {
    fontSize: 12,
    color: '#555555',
    fontFamily: fonts.Regular,
  },
  textItemBold: {
    fontFamily: fonts.SemiBold,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 15,
    marginTop: 100,
  },
  button: {
    flex: 1,
    borderRadius: 12,
    backgroundColor: colors.primary,
    height: 42,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#023e3f',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontFamily: fonts.SemiBold,
  },
});

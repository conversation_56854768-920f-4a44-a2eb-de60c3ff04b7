import {
  Image,
  StyleSheet,
  Text,
  View,
  TextInputProps,
  ImageSourcePropType,
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import AppButton from '../common/AppButton';
import {colors, fonts} from '../../utilities/theme';
import Navigation from '../../navigation/Navigation';
interface Props extends TextInputProps {
  heading?: string;
  title?: string;
  cost?: number | string;
  date?: string;
  btnText?: string;
  onPressTrack?: () => void;
  imageProp?: string;
  btnShow?: boolean;
  onPressClose?: () => void;
  btnShowClose?: boolean;
}
const MyCartItem: React.FC<Props> = ({
  heading,
  title,
  cost,
  date,
  btnText,
  imageProp,
  onPressTrack,
  btnShow = false,
  onPressClose,
  btnShowClose = false,
}) => (
  <View>
    <View style={styles.itemContainer}>
      {btnShowClose && (
        <TouchableOpacity onPress={onPressClose} style={styles.cartValue}>
          <Text
            style={{
              color: colors.white,
              fontSize: 12,
              lineHeight: 11,
            }}>
            x
          </Text>
        </TouchableOpacity>
      )}
      <View style={{flexDirection: 'row'}}>
        <View style={{flex: 1, marginRight: 10}}>
          <Text style={styles.headingText}>{heading}</Text>
          <Text style={[styles.titleText, {color: colors.gray[50]}]}>
            {title}
          </Text>
          <View style={{flexDirection: 'row', marginTop: 10, marginBottom: 5}}>
            {!btnShow && (
              <View style={{flex: 1, marginRight: 10}}>
                <AppButton
                  title="Track Order"
                  onPress={onPressTrack}
                  titleStyle={styles.btnText}
                  customStyle={
                    [
                      styles.btnContainer,
                      {backgroundColor: colors.black},
                    ] as ViewStyle
                  }
                />
              </View>
            )}
            {!btnShow ? (
              <View style={{flex: 1}}>
                <AppButton
                  title={btnText}
                  titleStyle={styles.btnText}
                  customStyle={styles.btnContainer}
                />
              </View>
            ) : (
              <View
                style={{
                  flex: 1,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <AppButton
                  title={btnText}
                  titleStyle={styles.btnText}
                  customStyle={
                    [
                      styles.btnContainer,
                      {
                        alignSelf: 'flex-start',
                        backgroundColor:
                          btnText === 'Borrowed'
                            ? colors.black
                            : colors.primary,
                      },
                    ] as ViewStyle
                  }
                />
              </View>
            )}
          </View>
        </View>
        <View style={{flex: 0.5}}>
          <Image
            style={{height: 90, width: '100%'}}
            source={{uri: imageProp}} // Use the imageProp as the uri
            resizeMode="contain"
          />
        </View>
      </View>
      <View style={styles.costDateContainer}>
        <Text style={styles.costStyle}>$NZ{cost}</Text>
        <Text style={styles.dateStyle}>{date}</Text>
      </View>
    </View>
  </View>
);

export default MyCartItem;
const styles = StyleSheet.create({
  itemContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    marginTop: 2,
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    marginHorizontal: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  cartValue: {
    position: 'absolute',
    zIndex: 1,
    top: -2,
    right: -2,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 15,
    width: 18,
    height: 18,
  },
  headingText: {
    fontSize: 18,
    fontFamily: fonts.Bold,
    color: colors.primary,
  },

  titleText: {
    fontSize: 12,
    fontWeight: '300',
  },

  btnContainer: {
    height: 31,
    paddingHorizontal: 10,
    borderRadius: 15,
  },

  btnText: {
    fontFamily: fonts.Medium,
    fontSize: 9,
  },

  costDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  costStyle: {
    fontSize: 20,
    fontFamily: fonts.Bold,
    color: colors.black,
  },

  dateStyle: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },
});

import {
  StyleSheet,
  ViewStyle,
  TextInputProps,
  Text,
  TouchableOpacity,
  Image,
} from 'react-native';
import React from 'react';
import {colors, fonts} from '../../utilities/theme';
import {useUser} from '../../Hooks/UseContext';
import {images} from '../../assets/images';
interface Props extends TextInputProps {
  containerStyle?: ViewStyle;
  onPress?: () => void;
}

const ChatBoxBottom: React.FC<Props> = ({containerStyle, onPress}) => {
  const {user} = useUser();
  return (
    // <TouchableOpacity
    //   style={[styles.bottomContainer, containerStyle]}
    //   onPress={onPress}>
    //   <Text style={styles.bottomText}>Hey, Jasmin.</Text>
    //   <Image
    //     style={{width: 50, height: 50, borderRadius: 100}}
    //     source={user?.profileImage ? {uri: user.profileImage} : images.Avatar}
    //   />
    // </TouchableOpacity>
    <TouchableOpacity
      style={[styles.bottomContainer, containerStyle]}
      onPress={onPress}>
      <Text style={styles.bottomText}>
        Hey, {user?.name ? user.name.split(' ')[0] : ''}
      </Text>
      <Image
        style={{width: 50, height: 50, borderRadius: 100}}
        source={user?.profileImage ? {uri: user.profileImage} : images.Avatar}
      />
    </TouchableOpacity>
  );
};

export default ChatBoxBottom;

const styles = StyleSheet.create({
  bottomContainer: {
    paddingVertical: 5,
    paddingHorizontal: 20,
    position: 'absolute',
    backgroundColor: colors.white,
    right: 28,
    bottom: 25,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },

  bottomText: {
    fontSize: 12,
    fontFamily: fonts.Medium,
    color: colors.gray[250],
  },
});

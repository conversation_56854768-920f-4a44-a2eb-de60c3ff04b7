import {
  StyleSheet,
  Text,
  View,
  TextInputProps,
  TouchableOpacity,
  Image,
} from 'react-native';
import React, {ElementType} from 'react';
import {EditProfile} from '../../assets/svg';
import {colors, fonts} from '../../utilities/theme';
import {useUser} from '../../Hooks/UseContext';
import {images} from '../../assets/images';

interface Props extends TextInputProps {
  onPress?: () => void;
  title?: string;
  icon?: ElementType;
}

const ProfileContainer: React.FC<Props> = ({
  onPress,
  title,
  icon: IconComponent,
}) => {
  const {setUser, user} = useUser();
  return (
    <View style={styles.profileCard}>
      <View style={styles.profileContainerBox}>
        <Image
          style={styles.profileImg}
          source={user?.profileImage ? {uri: user.profileImage} : images.Avatar}
        />
        <View style={styles.onlineProfileDot} />
      </View>

      <View style={styles.textContainer}>
        <Text style={styles.profileCardHeading}>{user?.name}</Text>
        <Text style={styles.profileCardLabel}>
          {user?.email?.toLowerCase()}
        </Text>
        <TouchableOpacity onPress={onPress}>
          <EditProfile style={styles.editProfileIcon} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ProfileContainer;

const styles = StyleSheet.create({
  profileCard: {
    backgroundColor: colors.white,
    marginVertical: 10,
    paddingVertical: 15,
    marginTop: 40,
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 15,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },

  profileContainerBox: {
    width: 112,
    height: 112,
  },
  profileImg: {
    width: '100%',
    height: '100%',
    borderRadius: 80, // Circular shape
    overflow: 'hidden',
  },

  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.6)', // Semi-transparent background
  },

  onlineProfileDot: {
    position: 'absolute',
    bottom: 0,
    right: 10,
    borderWidth: 4,
    backgroundColor: colors.primary,
    borderColor: colors.white,
    borderRadius: 25,
    width: 22,
    height: 22,
  },

  textContainer: {
    flex: 1, // Allows this container to take up remaining space
    paddingLeft: 15,
    justifyContent: 'flex-start', // Aligns content to the top
  },

  profileCardHeading: {
    fontSize: 20,
    fontFamily: fonts.Bold,
    color: colors.black,
  },

  profileCardLabel: {
    fontSize: 16,
    fontFamily: fonts.Regular,
    color: colors.primary,
  },

  editProfileIcon: {
    alignSelf: 'flex-end',
    marginTop: 20,
    // marginBottom: 5,
  },
});

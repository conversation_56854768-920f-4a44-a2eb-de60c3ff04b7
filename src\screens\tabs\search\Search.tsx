import {
  StyleSheet,
  View,
  FlatList,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import {colors} from '../../../utilities/theme';
import {
  AuthHeader,
  CategoryItem,
  ProductItem,
  SearchBar,
} from '../../../component';
import ChatBoxBottom from '../../../component/home/<USER>';
import firestore, {
  FirebaseFirestoreTypes,
} from '@react-native-firebase/firestore';
import {ICategories} from '../../../interfaces/Products.ts';
import {IProducts} from '../../../interfaces/Products.ts';
import {useUser} from '../../../Hooks/UseContext.tsx';
import UseProduct from '../../../Hooks/UseProduct.tsx';
import {TourGuideZoneByPosition} from 'rn-tourguide';
import {useTourGuideController} from 'rn-tourguide';
import {LeftIcon, MyCart, NotificationIcon} from '../../../assets/svg/index.ts';
import BorrowTermsandCondition from '../../../model/BorrowTermsandCondition.tsx';
import BorrowSuccessModal from '../../../model/BorrowSuccessModal.tsx';
type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'Search'
>;

const Search: React.FC<Props> = ({navigation}) => {
  const {onPressWishList, onPressMyCart} = UseProduct();
  const flatListRef = useRef<FlatList<ICategories>>(null);

  const [isModalVisible, setModalVisible] = useState(false);
  const {user, setUser} = useUser();
  const [categories, setCategories] = useState<ICategories[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<ICategories>({
    id: 'All',
    categoryLabel: 'All',
    createdAt: null,
    updatedAt: null,
  });
  const [products, setProducts] = useState<IProducts[]>([]);
  const [borrowItem, setBorrowItem] = useState<IProducts>();

  const [fetchProductsListLoading, setFetchProductsListLoading] =
    useState(false);
  const [categoryLoading, setCategoryLoading] = useState(false);

  const [searchText, setSearchText] = useState('');
  const [filteredProducts, setFilteredProducts] = useState<IProducts[]>([]); // State for filtered products

  const [totalCartItems, setTotalCartItems] = useState(0);
  const [borrowLoading, setBorrowLoading] = useState<{[key: string]: boolean}>(
    {},
  );
  const [purchaseLoading, setPurchaseLoading] = useState<{
    [key: string]: boolean;
  }>({});
  const {start, canStart, eventEmitter, getCurrentStep} =
    useTourGuideController();
  const [currentZone, setCurrentZone] = useState(0);
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [admin, setAdmin] = useState<IUser>();

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <View style={{marginLeft: 32, marginTop: 4}}>
          <LeftIcon />
        </View>
      ),
      headerTitle: '',
      headerRight: () => (
        <TouchableOpacity
          style={styles.notificationContainer}
          onPress={() => navigation.navigate('Notification')}>
          <NotificationIcon />
        </TouchableOpacity>
      ),
    });
  }, []);

  useEffect(() => {
    const step = getCurrentStep();
    if (step && step?.order !== currentZone) {
      setCurrentZone(step.order);
    }
  }, [getCurrentStep, currentZone]);

  useEffect(() => {
    if (!user) return;
    if (canStart && !user?.isTourViewed) {
      start(0);
    }
  }, [canStart, user?.isTourViewed]);

  const handleOnStart = async () => {
    if (!user) return;
    try {
      setUser({
        ...user,
        isTourViewed: true,
      });
      await firestore().collection('Users').doc(user.userId).update({
        isTourViewed: true,
      });
    } catch (error) {
      console.error('Error updating tour status:', error);
    }
  };

  useEffect(() => {
    if (!eventEmitter) return;
    eventEmitter.on('start', handleOnStart);
    return () => {
      eventEmitter.off('start', handleOnStart);
    };
  }, []);
  const toolTipHomeText = [
    'Your cart is waiting! Tap here to view your selected items ready for borrowing or purchasing.',
    'Looking for something specific? Just type here to find products quickly!',
    `Tap to browse categories and discover the products you're looking for!`,
    'Tap for more details! View product info, add to your wishlist, or choose to borrow or purchase.',
  ];

  const fetchCartItems = () => {
    if (!user) {
      return () => {};
    }
    const userRef = firestore().collection('Users').doc(user.userId);
    const unsubscribe = userRef.onSnapshot(
      docSnapshot => {
        if (docSnapshot.exists) {
          const userData = docSnapshot.data();
          const myCart = userData?.myCart || {};
          const totalItems = Object.keys(myCart).length;
          setTotalCartItems(totalItems);
        } else {
          setTotalCartItems(0);
        }
      },
      error => {
        console.log('Error while fetching cart items:', error);
      },
    );

    return unsubscribe;
  };

  useEffect(() => {
    const unsubscribe = fetchCartItems();
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const fetchCategories = async () => {
      setCategoryLoading(true);
      try {
        const querySnapshot = await firestore()
          .collection('Categories')
          .orderBy('updatedAt', 'desc')
          .get();
        const categories: ICategories[] = [];
        querySnapshot.forEach(doc => {
          categories.push({id: doc.id, ...doc.data()} as ICategories);
        });
        const categoriesWithAll: ICategories[] = [
          {id: 'all', categoryLabel: 'All', createdAt: null, updatedAt: null},
          ...categories,
        ];
        setCategories(categoriesWithAll);
      } catch (error) {
        console.error('Error fetching products: ', error);
      } finally {
        setCategoryLoading(false);
      }
    };
    fetchCategories();
  }, []);

  const handleSearchTextChange = (text: string) => {
    setSearchText(text);
  };
  useEffect(() => {
    const filterProducts = () => {
      const lowercasedFilter = searchText.toLowerCase();
      const filtered = products.filter(
        product =>
          product.productTitle &&
          product.productTitle.toLowerCase().includes(lowercasedFilter),
      );
      setFilteredProducts(filtered);
    };

    filterProducts();
  }, [searchText, products]);

  useEffect(() => {
    setFetchProductsListLoading(true);

    let productRef = firestore()
      .collection('Products')
      .orderBy('updatedAt', 'desc');

    if (selectedCategory && selectedCategory.categoryLabel !== 'All') {
      productRef = productRef.where('category', '==', selectedCategory.id);
    }

    const unsubscribe = productRef.onSnapshot(
      snapshot => {
        const _products: IProducts[] = snapshot.docs.map(
          doc =>
            ({
              ...doc.data(),
              id: doc.id,
            } as IProducts),
        );

        setProducts(_products);
        setFetchProductsListLoading(false);
      },
      error => {
        console.log('Error fetching products:', error);
        setFetchProductsListLoading(false);
      },
    );

    return unsubscribe; // Cleanup on unmount
  }, [selectedCategory]);

  const handleFavoritePress = async (item: IProducts) => {
    const updatedWishList = await onPressWishList(item);
    if (updatedWishList) {
      setProducts(prevProducts => {
        const currentProducts = prevProducts || [];
        return currentProducts.map(product =>
          product.id === item.id
            ? {...product, wishList: updatedWishList}
            : product,
        );
      });
    }
  };

  const handleBorrowPress = async (item: IProducts) => {
    setBorrowLoading(prevState => ({
      ...prevState,
      [item.id]: true,
    }));
    const updatedMyCart = await onPressMyCart(item, 'Borrowed');
    setBorrowLoading(prevState => ({
      ...prevState,
      [item.id]: false,
    }));
    if (updatedMyCart) {
      setSuccessModalVisible(true);
      // navigation.navigate('MyCart');
    }
  };

  const handlePurchasePress = async (item: IProducts) => {
    setPurchaseLoading(prevState => ({
      ...prevState,
      [item.id]: true,
    }));
    const updatedMyCart = await onPressMyCart(item, 'Purchased');
    setPurchaseLoading(prevState => ({
      ...prevState,
      [item.id]: false,
    }));
    if (updatedMyCart) {
      navigation.navigate('MyCart');
    }
  };

  useEffect(() => {
    const unsubscribe = firestore()
      .collection('Users')
      .where('userType', '==', 'Admin')
      .limit(1) // Ensures we fetch only one Admin user
      .onSnapshot(
        querySnapshot => {
          try {
            if (!querySnapshot.empty) {
              const adminDoc = querySnapshot.docs[0]; // Get the first document
              const adminData = {
                ...adminDoc.data(),
                userId: adminDoc.id,
              } as IUser;

              setAdmin(adminData); // Store a single object instead of an array
            } else {
              setAdmin(null); // No admin found
            }
          } catch (error) {
            console.error('Error processing Admin snapshot: ', error);
          }
        },
        error => {
          console.error('Error listening to Admin: ', error);
        },
      );

    return () => unsubscribe();
  }, []);

  return (
    <View style={styles.container}>
      <TourGuideZoneByPosition
        zone={0}
        text={toolTipHomeText[0]}
        top={Dimensions.get('window').height * 0.04}
        left="46%"
        width="10%"
        height={Dimensions.get('window').height * 0.09}
        isTourGuide={true}
      />

      <TourGuideZoneByPosition
        zone={1}
        text={toolTipHomeText[1]}
        top={Dimensions.get('window').height * 0.13}
        left="10%"
        right="10%"
        height={Dimensions.get('window').height * 0.09}
        isTourGuide={true}
        containerStyle={{alignSelf: 'center'}}
      />
      <SearchBar
        placeholder="Search for a product here.."
        containerStyle={{marginTop: 25, marginHorizontal: 32, marginBottom: 8}}
        onChangeText={handleSearchTextChange}
      />
      <TourGuideZoneByPosition
        zone={2}
        text={toolTipHomeText[2]}
        top={Dimensions.get('window').height * 0.22}
        left="10%"
        right="10%"
        height={Dimensions.get('window').height * 0.09}
        isTourGuide={true}
      />
      <FlatList
        showsVerticalScrollIndicator={false}
        data={Array.from({length: 1})}
        ListHeaderComponent={
          <>
            {categoryLoading ? (
              <ActivityIndicator
                size="large"
                color={colors.primary}
                style={{marginTop: 20}}
              />
            ) : (
              <FlatList
                ref={flatListRef}
                horizontal
                showsHorizontalScrollIndicator={false}
                data={categories}
                keyExtractor={item => item.id}
                renderItem={({item, index}) => (
                  <CategoryItem
                    title={item.categoryLabel || ''}
                    selected={
                      selectedCategory.categoryLabel === item.categoryLabel
                    }
                    onPress={() => {
                      setSelectedCategory(item);
                      flatListRef.current?.scrollToIndex({
                        index,
                        animated: true,
                        viewPosition: 0.9, // Center the item (0 is left, 0.5 is center, 1 is right)
                      });
                    }}
                  />
                )}
                contentContainerStyle={styles.contentContainerStyle}
              />
            )}

            <TourGuideZoneByPosition
              zone={3}
              text={toolTipHomeText[3]}
              top={Dimensions.get('window').height * 0.12}
              left="10%"
              width="80%"
              height={Dimensions.get('window').height * 0.54}
              isTourGuide={true}
            />
            {fetchProductsListLoading ? (
              <ActivityIndicator
                size="large"
                color={colors.primary}
                style={{marginTop: 200}}
              />
            ) : filteredProducts.length === 0 ? (
              <Text style={styles.emptyMessage}>
                There is no product for this category.
              </Text>
            ) : (
              <View style={styles.productContainer}>
                <FlatList
                  showsHorizontalScrollIndicator={false}
                  data={filteredProducts}
                  keyExtractor={item => item.id}
                  renderItem={({item}) => (
                    <ProductItem
                      productTitle={item.productTitle}
                      productLabel={item.productLabel}
                      imageProp={item.image}
                      price={item.price ?? 0}
                      ratingStar={item.ratingStar ?? 0.0}
                      ratingReview={item.ratingReview ?? 0}
                      onPress={() =>
                        navigation.navigate('ProductDetails', {
                          id: item.id,
                          productTitle: item.productTitle,
                          productLabel: item.productLabel,
                          category: item.category,
                          description: item.description,
                          price: item.price ?? 0,
                          ratingStar: item.ratingStar ?? 0.0,
                          ratingReview: item.ratingReview ?? 0,
                          image: item.image,
                          wishList: item.wishList,
                        })
                      }
                      onPressPurchase={() => handlePurchasePress(item)}
                      purchaseLoading={purchaseLoading[item.id] || false}
                      onPressBarrow={() => {
                        setBorrowItem(item);
                        setModalVisible(true);
                      }}
                      borrowLoading={borrowLoading[item.id] || false}
                      onPressFavorite={() => handleFavoritePress(item)}
                      isFavorite={
                        !!(
                          item.wishList &&
                          user?.userId &&
                          item.wishList.includes(user.userId)
                        )
                      }
                      containerStyle={{marginBottom: 15}}
                    />
                  )}
                  contentContainerStyle={{paddingBottom: 50}}
                />
              </View>
            )}
          </>
        }
        renderItem={() => null}
      />

      <ChatBoxBottom
        containerStyle={{right: 32, bottom: 5}}
        onPress={() =>
          navigation.navigate('ChatDetails', {recipientUser: admin})
        }
      />
      <BorrowTermsandCondition
        isModalVisible={isModalVisible}
        setModalVisible={setModalVisible}
        onCanclePress={() => setModalVisible(false)}
        onPressBorrow={() => {
          setModalVisible(false);
          borrowItem && handleBorrowPress(borrowItem);
        }}
      />
      <BorrowSuccessModal
        borrowSuccessModal={successModalVisible}
        setBorrowSuccessModal={setSuccessModalVisible}
        onPressOk={() => {
          setSuccessModalVisible(false);
          setTimeout(() => {
            navigation.navigate('MyCart');
          }, 300);
        }}
        onBackdropPress={() => {
          setSuccessModalVisible(false);
          setTimeout(() => {
            navigation.navigate('MyCart');
          }, 300);
        }}
        productTitle={borrowItem?.productTitle || ''}
      />
    </View>
  );
};

export default Search;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentContainerStyle: {
    paddingHorizontal: 32,
    gap: 10,
    marginTop: 16,
    marginBottom: 4,
  },
  productContainer: {
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 90,
  },
  emptyMessage: {
    marginHorizontal: 32,
    textAlign: 'center',
    marginTop: 200,
    fontSize: 16,
    color: colors.gray[50],
  },
  notificationContainer: {
    backgroundColor: colors.white,
    width: 48,
    height: 48,
    borderRadius: 18,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    justifyContent: 'center',
    marginRight: 32,
    marginTop: 4,
  },
});

import React, {createContext, useContext, useEffect, useState} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {IUser, IUserContext} from '../interfaces/IUser';
import SplashScreen from 'react-native-splash-screen';

interface AuthContextProps {
  firstLaunch: boolean;
  setFirstLaunch: (val: boolean) => void;
}

const UserContext = createContext<
  | (IUserContext & {
      firstLaunch: boolean;
      setFirstLaunch: (val: boolean) => void;
    })
  | undefined
>(undefined);
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}
export function UserProvider({children}: {children: any}) {
  const [user, setUser] = useState<IUser>();
  const [loadingSession, setLoadingSession] = useState(true);
  const [firstLaunch, setFirstLaunch] = useState(true);

  const retrieveFirstLaunch = async () => {
    const isFirstLaunchDone = await AsyncStorage.getItem('appLaunched');
    if (isFirstLaunchDone === 'false') {
      setFirstLaunch(false);
    } else {
      setFirstLaunch(true);
    }
  };

  const retrieveData = async () => {
    const currentUserData = await AsyncStorage.getItem('userData');
    if (currentUserData) {
      setUser(JSON.parse(currentUserData));
    }
    setLoadingSession(false);
  };
  const setCurrentUserDatalocally = (data: IUser) => {
    if (data) {
      AsyncStorage.setItem('userData', JSON.stringify(data));
    }
  };
  useEffect(() => {
    retrieveData();
    retrieveFirstLaunch();
  }, []);
  useEffect(() => {
    if (user) setCurrentUserDatalocally(user);
  }, [user]);

  useEffect(() => {
    setTimeout(() => {
      SplashScreen.hide();
    }, 1500);
  }, []);

  if (loadingSession) return null;

  return (
    <UserContext.Provider value={{user, setUser, firstLaunch, setFirstLaunch}}>
      {children}
    </UserContext.Provider>
  );
}
